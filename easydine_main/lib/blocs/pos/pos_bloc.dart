import 'dart:convert';

import 'package:easydine_main/models/cartItem.dart';
import 'package:easydine_main/models/cart_models.dart';
import 'package:easydine_main/models/customization_models.dart';
import 'package:easydine_main/models/menuItem.dart';
import 'package:easydine_main/services/menu_service.dart';
import 'package:easydine_main/services/cart_service.dart';
import 'package:easydine_main/services/customization_service.dart';
import 'package:easydine_main/services/order_service.dart';
import 'package:easydine_main/services/order_type_preference_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../cart/cart_bloc.dart';
import '../cart/cart_event.dart' as cart_events;
import '../session/session_bloc.dart';
import 'pos_event.dart';
import 'pos_state.dart';

class POSBloc extends Bloc<POSEvent, POSState> {
  final CartBloc cartBloc;
  final SessionBloc sessionBloc;

  POSBloc({required this.cartBloc, required this.sessionBloc})
      : super(const POSState()) {
    on<CategorySelected>(_onCategorySelected);
    on<SearchMenuItems>(_onSearchMenuItems);
    on<AddToCart>(_onAddToCart);
    on<AddCustomizedItemToCart>(_onAddCustomizedItemToCart);
    on<UpdateCartItemQuantity>(_onUpdateCartItemQuantity);
    on<RemoveFromCart>(_onRemoveFromCart);
    on<ClearCart>(_onClearCart);
    on<PlaceOrder>(_onPlaceOrder);
    on<AddItemsToOrder>(_onAddItemsToOrder);
    on<UpdateOrderPriority>(_onUpdateOrderPriority);
    on<SyncWithServerCart>(_onSyncWithServerCart);
    on<LoadMenuItems>(_onLoadMenuItems);
    on<AddToServerCart>(_onAddToServerCart);
    on<RemoveFromServerCart>(_onRemoveFromServerCart);
    on<ClearServerCart>(_onClearServerCart);
    on<SyncCartWithServer>(_onSyncCartWithServer);
    on<InitializePOS>(_onInitializePOS);
    on<LoadExistingCart>(_onLoadExistingCart);
  }

  void _onCategorySelected(CategorySelected event, Emitter<POSState> emit) {
    emit(state.copyWith(
      selectedCategory: event.category,
      searchQuery: '',
      filteredItems: [],
    ));
  }

  void _onSearchMenuItems(SearchMenuItems event, Emitter<POSState> emit) {
    final query = event.query.toLowerCase();

    if (query.isEmpty) {
      emit(state.copyWith(
        searchQuery: '',
        filteredItems: [],
        selectedCategory: null,
      ));
      return;
    }

    final allItems = MenuService.getAllMenuItems();
    final filteredItems = allItems.where((item) {
      return item.name.toLowerCase().contains(query) ||
          item.description.toLowerCase().contains(query) ||
          item.category.toLowerCase().contains(query);
    }).toList();

    emit(state.copyWith(
      searchQuery: query,
      filteredItems: filteredItems,
      selectedCategory: null,
    ));
  }

  Future<void> _onAddToCart(AddToCart event, Emitter<POSState> emit) async {
    try {
      debugPrint('🛒 POSBloc: Adding item to cart...');
      debugPrint('🛒 POSBloc: Item ID: ${event.id}');
      debugPrint('🛒 POSBloc: Item name: ${event.name}');
      debugPrint('🛒 POSBloc: Item price: ${event.price}');
      debugPrint(
          '🛒 POSBloc: Event customization data: ${event.customization}');

      // For standard add to cart (no customization)
      final request = AddItemToCartRequest(
        quantity: 1,
        dishId: event.id,
        type: "standard",
        notes: event.customization?['notes'],
      );

      debugPrint('🛒 POSBloc: Standard item request: ${request.toJson()}');
      cartBloc.add(cart_events.AddItemToCart(request: request));

      debugPrint('✅ POSBloc: Item add request sent to CartBloc');
    } catch (e) {
      debugPrint('❌ POSBloc: Error adding item to cart: $e');
      emit(state.copyWith(error: 'Failed to add item to cart'));
    }
  }

  /// Handle adding customization to an existing cart item
  Future<void> _onAddCustomizedItemToCart(
      AddCustomizedItemToCart event, Emitter<POSState> emit) async {
    try {
      debugPrint('🛒 POSBloc: Adding customization to cart item...');
      debugPrint('🛒 POSBloc: Cart Item ID: ${event.cartItemId}');
      debugPrint(
          '🛒 POSBloc: Customization data: ${jsonEncode(event.customizationData)}');

      // Send the customization update directly to the endpoint
      final success = await CartService.updateItem(
          event.cartItemId, event.customizationData);

      if (success) {
        debugPrint(
            '✅ POSBloc: Successfully updated cart item with customization');
        // Refresh the cart to show updated item
        cartBloc.add(cart_events.RefreshCart());
      } else {
        debugPrint('❌ POSBloc: Failed to update cart item with customization');
        emit(state.copyWith(error: 'Failed to add customization to cart item'));
      }
    } catch (e) {
      debugPrint('❌ POSBloc: Error adding customization to cart item: $e');
      emit(state.copyWith(error: 'Failed to add customization to cart item'));
    }
  }

  void _onUpdateCartItemQuantity(
      UpdateCartItemQuantity event, Emitter<POSState> emit) {
    try {
      debugPrint('🛒 POSBloc: Updating cart item quantity via CartBloc...');

      // Update quantity via CartBloc
      cartBloc.add(cart_events.UpdateCartItemQuantity(
        cartItemId: event.id,
        quantity: event.quantity,
      ));
      final req = CartService.updateItemQuantity(
        event.id,
        event.quantity,
      );

      debugPrint('✅ POSBloc: Quantity update request sent to CartBloc');
    } catch (e) {
      debugPrint('❌ POSBloc: Error updating cart item quantity: $e');
      emit(state.copyWith(error: 'Failed to update item quantity'));
    }
  }

  void _onRemoveFromCart(RemoveFromCart event, Emitter<POSState> emit) {
    try {
      debugPrint('🛒 POSBloc: Removing item from cart via CartBloc...');

      // Remove item via CartBloc
      cartBloc.add(cart_events.RemoveCartItem(cartItemId: event.id));

      debugPrint('✅ POSBloc: Item removal request sent to CartBloc');
    } catch (e) {
      debugPrint('❌ POSBloc: Error removing item from cart: $e');
      emit(state.copyWith(error: 'Failed to remove item from cart'));
    }
  }

  void _onClearCart(ClearCart event, Emitter<POSState> emit) {
    try {
      debugPrint('🛒 POSBloc: Clearing cart via CartBloc...');

      // Clear cart via CartBloc
      cartBloc.add(cart_events.ClearCart());

      debugPrint('✅ POSBloc: Cart clear request sent to CartBloc');
    } catch (e) {
      debugPrint('❌ POSBloc: Error clearing cart: $e');
      emit(state.copyWith(error: 'Failed to clear cart'));
    }
  }

  Future<void> _onPlaceOrder(PlaceOrder event, Emitter<POSState> emit) async {
    emit(state.copyWith(isProcessing: true));

    try {
      debugPrint('🛒 POSBloc: Placing order via CartBloc...');
      debugPrint('🛒 POSBloc: Order type: ${event.orderType}');
      debugPrint('🛒 POSBloc: Table number: ${event.tableNumber}');

      // Get waiter ID from session bloc
      final waiterId = sessionBloc.state.waiterId;
      if (waiterId == null) {
        debugPrint('❌ POSBloc: No waiter ID found in session');
        emit(state.copyWith(
          isProcessing: false,
          error: 'No waiter session found. Please log in again.',
        ));
        return;
      }

      debugPrint('🛒 POSBloc: Using waiter ID from session: $waiterId');

      // Get order types to map the order type string to ID
      String? orderTypeId;
      String? tableId;

      debugPrint('🛒 POSBloc: Fetching order types...');
      final orderTypes = await CustomizationService.getOrderTypes();

      if (orderTypes == null || orderTypes.isEmpty) {
        debugPrint(
            '❌ POSBloc: Failed to fetch order types or no order types available');
        emit(state.copyWith(
          isProcessing: false,
          error: 'Failed to fetch order types',
        ));
        return;
      }

      // Find the matching order type using comprehensive matching
      final matchingOrderType = _findOrderType(orderTypes, event.orderType);

      if (matchingOrderType == null) {
        debugPrint('❌ POSBloc: Order type "${event.orderType}" not found');
        debugPrint(
            'Available order types: ${orderTypes.map((e) => '${e.name} (${e.reservedName})').join(', ')}');
        emit(state.copyWith(
          isProcessing: false,
          error: 'Order type "${event.orderType}" not found',
        ));
        return;
      }

      orderTypeId = matchingOrderType.orderTypeId;

      // Determine if table ID should be included based on order type
      final orderTypeName = matchingOrderType.reservedName.toLowerCase();
      final requiresTable = _orderTypeRequiresTable(orderTypeName);

      if (requiresTable &&
          (event.tableNumber == null || event.tableNumber!.isEmpty)) {
        debugPrint(
            '❌ POSBloc: Table number required for order type: ${matchingOrderType.name}');
        emit(state.copyWith(
          isProcessing: false,
          error:
              'Table number is required for ${matchingOrderType.name} orders',
        ));
        return;
      }

      tableId = requiresTable ? event.tableNumber : null;

      debugPrint(
          '🛒 POSBloc: Using order type: ${matchingOrderType.name} (ID: $orderTypeId)');
      debugPrint('🛒 POSBloc: Table ID: ${tableId ?? 'N/A (not required)'}');

      // Create confirm cart request
      final request = ConfirmCartRequest(
        assignedWaiterId: waiterId,
        tableId: tableId,
        orderTypeId: orderTypeId,
        numberOfPeople: 1, // Default to 1 person, can be enhanced later to get from UI
      );

      debugPrint('🛒 POSBloc: Confirm cart request: ${request.toJson()}');

      // Confirm cart (place order) via CartBloc
      cartBloc.add(cart_events.ConfirmCart(request: request));

      emit(state.copyWith(
        isProcessing: false,
        currentOrderId: event.orderId,
        currentPriority: event.priority,
      ));

      debugPrint('✅ POSBloc: Order placement request sent to CartBloc');
    } catch (e) {
      debugPrint('❌ POSBloc: Error placing order: $e');
      emit(state.copyWith(
        isProcessing: false,
        error: 'Failed to place order: $e',
      ));
    }
  }

  /// Add items to existing order
  Future<void> _onAddItemsToOrder(
      AddItemsToOrder event, Emitter<POSState> emit) async {
    emit(state.copyWith(isProcessing: true));

    try {
      debugPrint('🛒 POSBloc: Adding items to order ${event.orderDetailId}');

      // Get current cart ID
      final cartState = cartBloc.state;
      if (cartState.currentCart == null) {
        debugPrint('❌ POSBloc: No cart available to add to order');
        emit(state.copyWith(
          isProcessing: false,
          error: 'No items in cart to add to order',
        ));
        return;
      }

      final cartId = cartState.currentCart!.cartId;
      debugPrint('🛒 POSBloc: Using cart ID: $cartId');

      // Call the API to add items to order
      final success =
          await OrderService.addItemsToOrder(event.orderDetailId, cartId);

      if (success) {
        debugPrint('✅ POSBloc: Successfully added items to order');

        // Clear the cart after successful addition
        cartBloc.add(cart_events.ClearCart());

        emit(state.copyWith(
          isProcessing: false,
          currentOrderId: event.orderDetailId,
        ));
      } else {
        debugPrint('❌ POSBloc: Failed to add items to order');
        emit(state.copyWith(
          isProcessing: false,
          error: 'Failed to add items to order',
        ));
      }
    } catch (e) {
      debugPrint('❌ POSBloc: Error adding items to order: $e');
      emit(state.copyWith(
        isProcessing: false,
        error: 'Error adding items to order: $e',
      ));
    }
  }

// Helper method to find matching order type with comprehensive search
  OrderType? _findOrderType(List<OrderType> orderTypes, String searchTerm) {
    if (orderTypes.isEmpty) return null;

    debugPrint('🔍 POSBloc: Searching for order type: "$searchTerm"');

    // Strategy 1: Direct UUID match
    if (_isValidUUID(searchTerm)) {
      final byId =
          orderTypes.where((ot) => ot.orderTypeId == searchTerm).firstOrNull;
      if (byId != null) {
        debugPrint('✅ Found by UUID: ${byId.name}');
        return byId;
      }
    }

    // Strategy 2: Exact name match (case-insensitive)
    final exactMatch = orderTypes
        .where((ot) =>
            ot.name.toLowerCase() == searchTerm.toLowerCase() ||
            ot.reservedName.toLowerCase() == searchTerm.toLowerCase())
        .firstOrNull;

    if (exactMatch != null) {
      debugPrint('✅ Found by exact name: ${exactMatch.name}');
      return exactMatch;
    }

    // Strategy 3: Common alias matching
    final aliasMap = <String, String>{
      'dine_in': 'dine in',
      'dinein': 'dine in',
      'dine-in': 'dine in',
      'eat-in': 'dine in',
      'take-away': 'takeaway',
      'take away': 'takeaway',
      'collection': 'pickup',
      'collect': 'pickup',
      'phone': 'phone order',
      'call': 'phone order',
      'contactless': 'contactless dine in',
      'online': 'mobile',
      'app': 'mobile',
    };

    final normalizedSearch =
        searchTerm.toLowerCase().replaceAll(' ', '').replaceAll('-', '');
    for (final entry in aliasMap.entries) {
      if (normalizedSearch
          .contains(entry.key.replaceAll(' ', '').replaceAll('-', ''))) {
        final aliasMatch = orderTypes
            .where((ot) =>
                ot.name.toLowerCase() == entry.value ||
                ot.reservedName.toLowerCase() == entry.value)
            .firstOrNull;

        if (aliasMatch != null) {
          debugPrint(
              '✅ Found by alias "${entry.key}" -> "${entry.value}": ${aliasMatch.name}');
          return aliasMatch;
        }
      }
    }

    // Strategy 4: Partial match (contains)
    final partialMatch = orderTypes
        .where((ot) =>
            ot.name.toLowerCase().contains(searchTerm.toLowerCase()) ||
            ot.reservedName.toLowerCase().contains(searchTerm.toLowerCase()) ||
            searchTerm.toLowerCase().contains(ot.name.toLowerCase()) ||
            searchTerm.toLowerCase().contains(ot.reservedName.toLowerCase()))
        .firstOrNull;

    if (partialMatch != null) {
      debugPrint('✅ Found by partial match: ${partialMatch.name}');
      return partialMatch;
    }

    debugPrint('❌ No match found for: "$searchTerm"');
    return null;
  }

// Helper method to check if a string is a valid UUID
  bool _isValidUUID(String uuid) {
    final uuidRegex = RegExp(
      r'^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$',
      caseSensitive: false,
    );
    return uuidRegex.hasMatch(uuid);
  }

// Helper method to determine if an order type requires a table
  bool _orderTypeRequiresTable(String orderTypeName) {
    final orderType = orderTypeName.toLowerCase();

    // Order types that require table assignment
    final tableRequiredTypes = [
      'dine in',
      'contactless dine in',
      'site', // Restaurant site orders
    ];

    // Order types that don't require table assignment
    final noTableRequiredTypes = [
      'takeaway',
      'pickup',
      'delivery',
      'mobile',
      'phone order',
    ];

    // Check if it explicitly requires a table
    if (tableRequiredTypes.any((type) => orderType.contains(type))) {
      return true;
    }

    // Check if it explicitly doesn't require a table
    if (noTableRequiredTypes.any((type) => orderType.contains(type))) {
      return false;
    }

    // Default to requiring table for unknown types (safer approach)
    debugPrint(
        '⚠️ POSBloc: Unknown order type "$orderTypeName", defaulting to require table');
    return true;
  }

  void _onUpdateOrderPriority(
      UpdateOrderPriority event, Emitter<POSState> emit) {
    emit(state.copyWith(currentPriority: event.priority));
  }

  /// Sync local cart with server cart
  Future<void> _onSyncWithServerCart(
      SyncWithServerCart event, Emitter<POSState> emit) async {
    try {
      debugPrint('🔄 POSBloc: Syncing with server cart...');

      // Ensure we have a valid order type ID (required for cart operations)
      final orderTypeId = await OrderTypePreferenceService.ensureOrderTypeId();
      debugPrint('🔄 POSBloc: Using order type ID for cart sync: $orderTypeId');

      final serverCart = await CartService.getOrCreateCart(orderTypeId: orderTypeId);

      if (serverCart != null) {
        // Convert server cart items to local cart items for display
        final localCartItems = <CartItem>[];

        for (final serverItem in serverCart.items) {
          // Create a simplified local cart item for display
          // Note: This is a basic conversion - you may need to enhance this
          // based on your specific requirements for displaying server cart items
          localCartItems.add(CartItem(
            id: serverItem.id,
            name: serverItem
                .dishId, // You might want to fetch dish name from menu
            price: 0.0, // You might want to fetch price from menu
            totalPrice: 0.0, // Placeholder for total price
            quantity: serverItem.quantity,
            customization: {
              'notes': serverItem.notes,
              'type': serverItem.type,
              'allergyIds': serverItem.allergyIds,
              'dishAddons':
                  serverItem.dishAddons.map((e) => e.toJson()).toList(),
              'dishExtras':
                  serverItem.dishExtras.map((e) => e.toJson()).toList(),
              'dishSides': serverItem.dishSides.map((e) => e.toJson()).toList(),
              'dishBeverages':
                  serverItem.dishBeverages.map((e) => e.toJson()).toList(),
              'dishDesserts':
                  serverItem.dishDesserts.map((e) => e.toJson()).toList(),
            },
          ));
        }

        // Cart items are now managed by CartBloc, no need to update local state
        debugPrint(
            '✅ POSBloc: Synced ${localCartItems.length} items from server cart');
      }
    } catch (e) {
      debugPrint('❌ POSBloc: Error syncing with server cart: $e');
      emit(state.copyWith(error: 'Failed to sync with server cart'));
    }
  }

  /// Load menu items from server
  Future<void> _onLoadMenuItems(
      LoadMenuItems event, Emitter<POSState> emit) async {
    try {
      debugPrint('📋 POSBloc: Loading menu items...');

      // Get branch ID from SharedPreferences or use provided branchId
      String? branchId = event.branchId;
      if (branchId == null) {
        final prefs = await SharedPreferences.getInstance();
        branchId = prefs.getString('selected_branch_id') ?? 'default-branch-id';
      }

      // Load menu data from API
      final menuData = await MenuService.getActiveMenuWithAvailability(
        branchId: branchId,
        forPOS: true,
      );

      if (menuData != null) {
        // Convert menu sections to legacy format and group by section name
        Map<String, List<MenuItem>> grouped = {};

        for (var section in menuData.sections) {
          if (section.dishes.isNotEmpty) {
            final menuItems =
                MenuService.convertDishesToMenuItems(section.dishes);
            // Update category for each item to match section name
            final updatedItems = menuItems
                .map((item) => MenuItem(
                      id: item.id,
                      name: item.name,
                      price: item.price,
                      image: item.image,
                      category: section.name, // Use section name as category
                      description: item.description,
                      ingredients: item.ingredients,
                      prepTime: item.prepTime,
                      rating: item.rating,
                      isSpicy: item.isSpicy,
                      dietaryInfo: item.dietaryInfo,
                    ))
                .toList();

            grouped[section.name] = updatedItems;
          }
        }

        // Update the MenuService cache so CategoryList can access the data
        MenuService.updateCache(menuData, grouped);

        debugPrint(
            '✅ POSBloc: Menu data loaded and cached. Categories: ${grouped.keys.toList()}');

        // Reset state with "All" category selected and mark menu as loaded
        debugPrint(
            '🍽️ POSBloc: Setting isMenuLoaded = true and emitting state');
        emit(state.copyWith(
          selectedCategory: "All",
          searchQuery: '',
          filteredItems: [],
          isMenuLoaded: true,
        ));
        debugPrint(
            '🍽️ POSBloc: State emitted successfully with isMenuLoaded = true');
      } else {
        // Reset state even if menu loading failed
        emit(state.copyWith(
          selectedCategory: "All",
          searchQuery: '',
          filteredItems: [],
          isMenuLoaded: false,
        ));
      }
    } catch (e) {
      debugPrint('❌ POSBloc: Error loading menu items: $e');
      emit(state.copyWith(error: 'Failed to load menu items'));
    }
  }

  /// Add item directly to server cart
  Future<void> _onAddToServerCart(
      AddToServerCart event, Emitter<POSState> emit) async {
    try {
      debugPrint('🛒 POSBloc: Adding item to server cart...');
      final request = AddItemToCartRequest(
        quantity: event.quantity,
        dishId: event.dishId,
        type: event.type,
        notes: event.notes,
        allergyIds: event.allergyIds,
      );

      final success = await CartService.addItemToCart(request);
      if (success) {
        debugPrint('✅ POSBloc: Successfully added item to server cart');
        // Optionally sync local cart with server
        add(const SyncCartWithServer());
      } else {
        debugPrint('❌ POSBloc: Failed to add item to server cart');
        emit(state.copyWith(error: 'Failed to add item to cart'));
      }
    } catch (e) {
      debugPrint('❌ POSBloc: Error adding item to server cart: $e');
      emit(state.copyWith(error: 'Error adding item to cart: $e'));
    }
  }

  /// Remove item from server cart
  Future<void> _onRemoveFromServerCart(
      RemoveFromServerCart event, Emitter<POSState> emit) async {
    try {
      debugPrint('🗑️ POSBloc: Removing item from server cart...');
      final success = await CartService.deleteItemFromCart(event.cartItemId);

      if (success) {
        debugPrint('✅ POSBloc: Successfully removed item from server cart');
        // Sync local cart with server
        add(const SyncCartWithServer());
      } else {
        debugPrint('❌ POSBloc: Failed to remove item from server cart');
        emit(state.copyWith(error: 'Failed to remove item from cart'));
      }
    } catch (e) {
      debugPrint('❌ POSBloc: Error removing item from server cart: $e');
      emit(state.copyWith(error: 'Error removing item from cart: $e'));
    }
  }

  /// Clear server cart
  Future<void> _onClearServerCart(
      ClearServerCart event, Emitter<POSState> emit) async {
    try {
      debugPrint('🧹 POSBloc: Clearing server cart...');
      final success = await CartService.clearCart();

      if (success) {
        debugPrint('✅ POSBloc: Successfully cleared server cart');
        // Cart is now managed by CartBloc
      } else {
        debugPrint('❌ POSBloc: Failed to clear server cart');
        emit(state.copyWith(error: 'Failed to clear cart'));
      }
    } catch (e) {
      debugPrint('❌ POSBloc: Error clearing server cart: $e');
      emit(state.copyWith(error: 'Error clearing cart: $e'));
    }
  }

  /// Sync local cart with server cart
  Future<void> _onSyncCartWithServer(
      SyncCartWithServer event, Emitter<POSState> emit) async {
    try {
      debugPrint('🔄 POSBloc: Syncing local cart with server...');

      // Ensure we have a valid order type ID (required for cart operations)
      final orderTypeId = await OrderTypePreferenceService.ensureOrderTypeId();
      debugPrint('🔄 POSBloc: Using order type ID for cart sync: $orderTypeId');

      final serverCart = await CartService.getOrCreateCart(orderTypeId: orderTypeId);

      if (serverCart != null) {
        // Convert server cart items to local cart items for display
        final localCartItems = <CartItem>[];

        for (final serverItem in serverCart.items) {
          // Create a simplified local cart item for display
          localCartItems.add(CartItem(
            id: serverItem.id,
            name: serverItem
                .dishId, // You might want to fetch dish name from menu
            price: 0.0, // You might want to fetch price from menu
            quantity: serverItem.quantity,
            totalPrice: 0.0,
            customization: {
              'notes': serverItem.notes,
              'type': serverItem.type,
              'allergyIds': serverItem.allergyIds,
              'dishAddons':
                  serverItem.dishAddons.map((e) => e.toJson()).toList(),
              'dishExtras':
                  serverItem.dishExtras.map((e) => e.toJson()).toList(),
              'dishSides': serverItem.dishSides.map((e) => e.toJson()).toList(),
              'dishBeverages':
                  serverItem.dishBeverages.map((e) => e.toJson()).toList(),
              'dishDesserts':
                  serverItem.dishDesserts.map((e) => e.toJson()).toList(),
            },
          ));
        }

        // Cart items are now managed by CartBloc, no need to update local state
        debugPrint(
            '✅ POSBloc: Synced ${localCartItems.length} items from server cart');
      }
    } catch (e) {
      debugPrint('❌ POSBloc: Error syncing with server cart: $e');
      emit(state.copyWith(error: 'Failed to sync with server cart'));
    }
  }

  /// Initialize POS screen - load existing cart and menu data
  Future<void> _onInitializePOS(
      InitializePOS event, Emitter<POSState> emit) async {
    try {
      debugPrint('🚀 POSBloc: Initializing POS screen...');

      // Load existing cart via CartBloc
      cartBloc.add(cart_events.LoadCart());

      // Trigger menu loading to populate categories
      add(const LoadMenuItems());

      debugPrint('✅ POSBloc: Cart load and menu load requests sent');
    } catch (e) {
      debugPrint('❌ POSBloc: Error initializing POS: $e');
      emit(state.copyWith(error: 'Failed to initialize POS'));
    }
  }

  /// Load existing cart data
  Future<void> _onLoadExistingCart(
      LoadExistingCart event, Emitter<POSState> emit) async {
    try {
      debugPrint('🛒 POSBloc: Loading existing cart...');

      // Refresh cart data via CartBloc
      cartBloc.add(cart_events.RefreshCart());

      debugPrint('✅ POSBloc: Cart refresh request sent to CartBloc');
    } catch (e) {
      debugPrint('❌ POSBloc: Error loading existing cart: $e');
      emit(state.copyWith(error: 'Failed to load existing cart'));
    }
  }

  // Helper method to compare customizations
  bool _areCustomizationsEqual(
    Map<String, dynamic>? customization1,
    Map<String, dynamic>? customization2,
  ) {
    if (customization1 == null && customization2 == null) return true;
    if (customization1 == null || customization2 == null) return false;

    // Convert to string for deep comparison
    return jsonEncode(customization1) == jsonEncode(customization2);
  }
}
