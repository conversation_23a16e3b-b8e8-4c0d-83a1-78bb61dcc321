import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import '../../services/cart_service.dart';
import '../../services/order_type_preference_service.dart';
import 'cart_event.dart';
import 'cart_state.dart';

class CartBloc extends Bloc<CartEvent, CartState> {
  CartBloc() : super(const CartInitial()) {
    on<LoadCart>(_onLoadCart);
    on<RefreshCart>(_onRefreshCart);
    on<AddItemToCart>(_onAddItemToCart);
    on<UpdateCartItemQuantity>(_onUpdateCartItemQuantity);
    on<UpdateCartItem>(_onUpdateCartItem);
    on<RemoveCartItem>(_onRemoveCartItem);
    on<ClearCart>(_onClearCart);
    on<HoldCart>(_onHoldCart);
    on<ActivateCart>(_onActivateCart);
    on<ConfirmCart>(_onConfirmCart);
    on<DeleteCart>(_onDeleteCart);
    on<FetchAllCarts>(_onFetchAllCarts);
    on<SetCartError>(_onSetCartError);
    on<ClearCartError>(_onClearCartError);
  }

  /// Load or create cart for current staff member
  Future<void> _onLoadCart(LoadCart event, Emitter<CartState> emit) async {
    emit(const CartLoading());

    try {
      debugPrint('🛒 CartBloc: Loading cart...');

      // Get current order type ID from preferences
      final orderTypeId = await OrderTypePreferenceService.getCurrentOrderTypeId();
      debugPrint('🛒 CartBloc: Using order type ID for cart load: $orderTypeId');

      final cart = await CartService.getOrCreateCart(orderTypeId: orderTypeId);

      if (cart != null) {
        debugPrint('🛒 CartBloc: Cart loaded successfully');
        emit(CartLoaded(cart: cart));
      } else {
        debugPrint('❌ CartBloc: Failed to load cart');
        emit(const CartError(error: 'Failed to load cart'));
      }
    } catch (e) {
      debugPrint('❌ CartBloc: Error loading cart: $e');
      emit(CartError(error: 'Error loading cart: $e'));
    }
  }

  /// Refresh cart data
  Future<void> _onRefreshCart(
      RefreshCart event, Emitter<CartState> emit) async {
    if (state.currentCart != null) {
      emit(CartProcessing(
          currentCart: state.currentCart, allCarts: state.allCarts));
    }

    try {
      // Get current order type ID from preferences
      final orderTypeId = await OrderTypePreferenceService.getCurrentOrderTypeId();
      debugPrint('🛒 CartBloc: Using order type ID for cart refresh: $orderTypeId');

      final cart = await CartService.getOrCreateCart(orderTypeId: orderTypeId);

      if (cart != null) {
        emit(CartLoaded(
          cart: cart,
          allCarts: state.allCarts,
          originalOrderId: state.originalOrderId,
        ));
      } else {
        emit(CartError(error: 'Failed to refresh cart'));
      }
    } catch (e) {
      emit(CartError(error: 'Error refreshing cart: $e'));
    }
  }

  /// Add item to cart
  Future<void> _onAddItemToCart(
      AddItemToCart event, Emitter<CartState> emit) async {
    emit(CartProcessing(
        currentCart: state.currentCart, allCarts: state.allCarts));

    try {
      debugPrint('🛒 CartBloc: Adding item to cart...');
      final success = await CartService.addItemToCart(event.request);

      if (success) {
        debugPrint('🛒 CartBloc: Item added successfully, refreshing cart...');
        // Refresh cart to get updated data
        final orderTypeId = await OrderTypePreferenceService.getCurrentOrderTypeId();
        final updatedCart = await CartService.getOrCreateCart(orderTypeId: orderTypeId);
        if (updatedCart != null) {
          emit(CartLoaded(
            cart: updatedCart,
            allCarts: state.allCarts,
            originalOrderId: state.originalOrderId,
          ));
        } else {
          emit(const CartError(
              error: 'Failed to refresh cart after adding item'));
        }
      } else {
        debugPrint('❌ CartBloc: Failed to add item to cart');
        emit(CartError(error: 'Failed to add item to cart'));
      }
    } catch (e) {
      debugPrint('❌ CartBloc: Error adding item to cart: $e');
      emit(CartError(error: 'Error adding item to cart: $e'));
    }
  }

  /// Update item quantity
  Future<void> _onUpdateCartItemQuantity(
      UpdateCartItemQuantity event, Emitter<CartState> emit) async {
    debugPrint(
        '🛒 CartBloc: Starting quantity update for item ${event.cartItemId} to quantity ${event.quantity}');

    emit(CartProcessing(
        currentCart: state.currentCart, allCarts: state.allCarts));

    try {
      debugPrint('🛒 CartBloc: Calling CartService.updateItemQuantity...');
      final success = await CartService.updateItemQuantity(
          event.cartItemId, event.quantity);

      if (success) {
        debugPrint(
            '🛒 CartBloc: Quantity update successful, refreshing cart...');
        // Refresh cart to get updated data
        final orderTypeId = await OrderTypePreferenceService.getCurrentOrderTypeId();
        final updatedCart = await CartService.getOrCreateCart(orderTypeId: orderTypeId);
        if (updatedCart != null) {
          debugPrint(
              '🛒 CartBloc: Cart refreshed successfully. Items count: ${updatedCart.items.length}');
          // Log the updated quantities for debugging
          for (final item in updatedCart.items) {
            debugPrint(
                '🛒 CartBloc: Item ${item.id} has quantity ${item.quantity}');
          }
          emit(CartLoaded(
            cart: updatedCart,
            allCarts: state.allCarts,
            originalOrderId: state.originalOrderId,
          ));
        } else {
          debugPrint(
              '❌ CartBloc: Failed to refresh cart after updating quantity');
          emit(const CartError(
              error: 'Failed to refresh cart after updating quantity'));
        }
      } else {
        debugPrint('❌ CartBloc: Failed to update item quantity');
        emit(const CartError(error: 'Failed to update item quantity'));
      }
    } catch (e) {
      debugPrint('❌ CartBloc: Error updating item quantity: $e');
      emit(CartError(error: 'Error updating item quantity: $e'));
    }
  }

  /// Update item details
  Future<void> _onUpdateCartItem(
      UpdateCartItem event, Emitter<CartState> emit) async {
    emit(CartProcessing(
        currentCart: state.currentCart, allCarts: state.allCarts));

    try {
      final success =
          await CartService.updateItem(event.cartItemId, event.updates);

      if (success) {
        // Refresh cart to get updated data
        final orderTypeId = await OrderTypePreferenceService.getCurrentOrderTypeId();
        final updatedCart = await CartService.getOrCreateCart(orderTypeId: orderTypeId);
        if (updatedCart != null) {
          emit(CartLoaded(
            cart: updatedCart,
            allCarts: state.allCarts,
            originalOrderId: state.originalOrderId,
          ));
        } else {
          emit(const CartError(
              error: 'Failed to refresh cart after updating item'));
        }
      } else {
        emit(const CartError(error: 'Failed to update item'));
      }
    } catch (e) {
      emit(CartError(error: 'Error updating item: $e'));
    }
  }

  /// Remove item from cart
  Future<void> _onRemoveCartItem(
      RemoveCartItem event, Emitter<CartState> emit) async {
    emit(CartProcessing(
        currentCart: state.currentCart, allCarts: state.allCarts));

    try {
      final success = await CartService.deleteItemFromCart(event.cartItemId);

      if (success) {
        // Refresh cart to get updated data
        final orderTypeId = await OrderTypePreferenceService.getCurrentOrderTypeId();
        final updatedCart = await CartService.getOrCreateCart(orderTypeId: orderTypeId);
        if (updatedCart != null) {
          emit(CartLoaded(
            cart: updatedCart,
            allCarts: state.allCarts,
            originalOrderId: state.originalOrderId,
          ));
        } else {
          emit(const CartError(
              error: 'Failed to refresh cart after removing item'));
        }
      } else {
        emit(const CartError(error: 'Failed to remove item from cart'));
      }
    } catch (e) {
      emit(CartError(error: 'Error removing item from cart: $e'));
    }
  }

  /// Clear all items from cart
  Future<void> _onClearCart(ClearCart event, Emitter<CartState> emit) async {
    emit(CartProcessing(
        currentCart: state.currentCart, allCarts: state.allCarts));

    try {
      final success = await CartService.clearCart();

      if (success) {
        // Refresh cart to get updated data
        final orderTypeId = await OrderTypePreferenceService.getCurrentOrderTypeId();
        final updatedCart = await CartService.getOrCreateCart(orderTypeId: orderTypeId);
        if (updatedCart != null) {
          emit(CartLoaded(cart: updatedCart, allCarts: state.allCarts));
        } else {
          emit(const CartError(error: 'Failed to refresh cart after clearing'));
        }
      } else {
        emit(const CartError(error: 'Failed to clear cart'));
      }
    } catch (e) {
      emit(CartError(error: 'Error clearing cart: $e'));
    }
  }

  /// Set cart to hold status
  Future<void> _onHoldCart(HoldCart event, Emitter<CartState> emit) async {
    emit(CartProcessing(
        currentCart: state.currentCart, allCarts: state.allCarts));

    try {
      final success = await CartService.holdCart();

      if (success) {
        // Refresh cart to get updated status
        final orderTypeId = await OrderTypePreferenceService.getCurrentOrderTypeId();
        final updatedCart = await CartService.getOrCreateCart(orderTypeId: orderTypeId);
        if (updatedCart != null) {
          emit(CartLoaded(cart: updatedCart, allCarts: state.allCarts));
        } else {
          emit(const CartError(error: 'Failed to refresh cart after holding'));
        }
      } else {
        emit(const CartError(error: 'Failed to hold cart'));
      }
    } catch (e) {
      emit(CartError(error: 'Error holding cart: $e'));
    }
  }

  /// Activate cart
  Future<void> _onActivateCart(
      ActivateCart event, Emitter<CartState> emit) async {
    emit(CartProcessing(
        currentCart: state.currentCart, allCarts: state.allCarts));

    try {
      final success = await CartService.activateCart(event.cartId);

      if (success) {
        // Refresh cart to get updated status
        final orderTypeId = await OrderTypePreferenceService.getCurrentOrderTypeId();
        final updatedCart = await CartService.getOrCreateCart(orderTypeId: orderTypeId);
        if (updatedCart != null) {
          emit(CartLoaded(cart: updatedCart, allCarts: state.allCarts));
        } else {
          emit(const CartError(
              error: 'Failed to refresh cart after activation'));
        }
      } else {
        emit(const CartError(error: 'Failed to activate cart'));
      }
    } catch (e) {
      emit(CartError(error: 'Error activating cart: $e'));
    }
  }

  /// Confirm cart (place order)
  Future<void> _onConfirmCart(
      ConfirmCart event, Emitter<CartState> emit) async {
    emit(CartProcessing(
        currentCart: state.currentCart, allCarts: state.allCarts));

    try {
      final success = await CartService.confirmCart(event.request);

      if (success) {
        // After confirming, load a new cart
        final orderTypeId = await OrderTypePreferenceService.getCurrentOrderTypeId();
        final newCart = await CartService.getOrCreateCart(orderTypeId: orderTypeId);
        if (newCart != null) {
          emit(CartLoaded(cart: newCart, allCarts: state.allCarts));
        } else {
          emit(const CartError(
              error: 'Failed to create new cart after confirmation'));
        }
      } else {
        emit(const CartError(error: 'Failed to confirm cart'));
      }
    } catch (e) {
      emit(CartError(error: 'Error confirming cart: $e'));
    }
  }

  /// Delete cart
  Future<void> _onDeleteCart(DeleteCart event, Emitter<CartState> emit) async {
    emit(CartProcessing(
        currentCart: state.currentCart, allCarts: state.allCarts));

    try {
      final success = await CartService.deleteCart(event.cartId);

      if (success) {
        // Create a new cart after deletion
        final orderTypeId = await OrderTypePreferenceService.getCurrentOrderTypeId();
        final newCart = await CartService.getOrCreateCart(orderTypeId: orderTypeId);
        if (newCart != null) {
          emit(CartLoaded(cart: newCart, allCarts: state.allCarts));
        } else {
          emit(const CartError(
              error: 'Failed to create new cart after deletion'));
        }
      } else {
        emit(const CartError(error: 'Failed to delete cart'));
      }
    } catch (e) {
      emit(CartError(error: 'Error deleting cart: $e'));
    }
  }

  /// Fetch all carts
  Future<void> _onFetchAllCarts(
      FetchAllCarts event, Emitter<CartState> emit) async {
    try {
      final carts = await CartService.fetchAllCarts();

      if (carts != null) {
        emit(state.copyWith(allCarts: carts));
      } else {
        emit(state.copyWith(error: 'Failed to fetch all carts'));
      }
    } catch (e) {
      emit(state.copyWith(error: 'Error fetching all carts: $e'));
    }
  }

  /// Set error message
  void _onSetCartError(SetCartError event, Emitter<CartState> emit) {
    emit(state.copyWith(error: event.error));
  }

  /// Clear error message
  void _onClearCartError(ClearCartError event, Emitter<CartState> emit) {
    emit(state.copyWith(clearError: true));
  }
}
