import 'package:easydine_main/widgets/app_bar.dart';
import 'package:easydine_main/widgets/tiled_background.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:ui' as ui;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sizer/sizer.dart';
import '../../blocs/running_orders/running_orders_bloc.dart';
import '../../blocs/running_orders/running_orders_event.dart';
import '../../blocs/running_orders/running_orders_state.dart';
import '../../router/router_constants.dart';
import '../../services/order_service.dart';
import '../../services/thermal_print_service.dart';
import '../../services/promotion_service.dart';
import '../../services/payment_service.dart';
import '../../services/tip_service.dart';
import '../../services/order_type_preference_service.dart';
import '../../models/order_model.dart';
import '../../widgets/otp_verification_modal.dart';
import '../../widgets/enhanced_otp_verification_modal.dart';

import '../../widgets/running_order_card.dart';
import '../../widgets/order_status_dropdown.dart' as dropdown;
import '../../utils/currency_formatter.dart';

// Order Status Enum matching backend
enum OrderStatus {
  PENDING('PENDING', 'Pending', Colors.orange),
  IN_PREPARATION('IN_PREPARATION', 'In Preparation', Colors.blue),
  READY('READY', 'Ready', Colors.green),
  SERVED('SERVED', 'Served', Colors.purple),
  CANCELLED('CANCELLED', 'Cancelled', Colors.red),
  CHECKOUT('CHECKOUT', 'Checkout', Colors.teal),
  COMPLETED('COMPLETED', 'Completed', Colors.grey);

  const OrderStatus(this.value, this.displayName, this.color);
  final String value;
  final String displayName;
  final Color color;

  static OrderStatus fromString(String status) {
    return OrderStatus.values.firstWhere(
      (e) => e.value == status.toUpperCase(),
      orElse: () => OrderStatus.PENDING,
    );
  }
}

class RunningOrdersPage extends StatefulWidget {
  @override
  _RunningOrdersPageState createState() => _RunningOrdersPageState();
}

class _RunningOrdersPageState extends State<RunningOrdersPage>
    with SingleTickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late TabController _tabController;
  late RunningOrdersBloc _runningOrdersBloc;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this, initialIndex: 0);
    _runningOrdersBloc = RunningOrdersBloc();

    // Add listener for tab changes
    _tabController.addListener(_onTabChanged);

    // Fetch initial orders
    _runningOrdersBloc.add(FetchRunningOrders());

    debugPrint(
        '🔄 TabController initialized with index: ${_tabController.index}');
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    _runningOrdersBloc.close();
    super.dispose();
  }

  void _refreshOrders() {
    _runningOrdersBloc.add(FetchRunningOrders());
  }

  void _onTabChanged() {
    if (!_tabController.indexIsChanging) return;

    final category = _getTabCategory(_tabController.index);
    debugPrint('🔄 Tab changed to: $category (index: ${_tabController.index})');

    _runningOrdersBloc.add(FilterByCategory(category));
  }

  String _getTabCategory(int index) {
    switch (index) {
      case 0:
        return "All";
      case 1:
        return "Dine In";
      case 2:
        return "Takeaway";
      case 3:
        return "Delivery";
      default:
        return "All";
    }
  }

  void _handleOrderTap(Map<String, dynamic> order, BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      constraints: BoxConstraints(
        maxWidth: isLandscape
            ? MediaQuery.of(context).size.width * 0.95
            : MediaQuery.of(context).size.width * 0.75,
        maxHeight: MediaQuery.of(context).orientation == Orientation.portrait
            ? MediaQuery.of(context).size.height * 0.75
            : MediaQuery.of(context).size.height * 0.95,
      ),
      builder: (context) => SafeArea(
        child: Container(
          margin: isLandscape
              ? EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 16,
                )
              : null,
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(20),
              bottom: isLandscape ? Radius.circular(20) : Radius.zero,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (!isLandscape)
                Container(
                  width: 40,
                  height: 4,
                  margin: EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey[600],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              if (isLandscape)
                Align(
                  alignment: Alignment.topRight,
                  child: IconButton(
                    icon: Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                    padding: EdgeInsets.all(16),
                  ),
                ),
              Expanded(
                child: OrderDetailsModal(
                  order: order,
                  onRefresh: _refreshOrders,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleAddItems(Map<String, dynamic> order, BuildContext context) async {
    try {
      final orderId = order['orderDetailId'] as String?;
      if (orderId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Order ID not found'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      debugPrint('🛒 Adding items to order: $orderId');

      // Capture router reference before async operation
      final router = GoRouter.of(context);

      // Store order type preference before navigation
      final orderType = order['type']?.toString() ?? 'dine_in';
      await OrderTypePreferenceService.setOrderTypeByName(orderType);

      // Navigate to POS with special query parameter to indicate adding items to existing order
      router.goNamed(RouterConstants.pos, queryParameters: {
        'tableNumber': order['table']?.toString() ?? '',
        'orderId': orderId,
        'orderType': orderType,
        'addToOrder':
            'true', // Special flag to indicate adding items to existing order
      });
    } catch (e) {
      debugPrint('❌ Error navigating to add items: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error opening add items screen'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _handleCancelOrder(Map<String, dynamic> order, BuildContext context) {
    debugPrint("Cancelling${order.toString()}");
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          'Cancel Order',
          style: GoogleFonts.dmSans(color: Colors.white),
        ),
        content: Text(
          'Are you sure you want to cancel order #${order['id']}? This action cannot be undone.',
          style: GoogleFonts.dmSans(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Keep Order',
              style: GoogleFonts.dmSans(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            onPressed: () async {
              Navigator.pop(context);

              try {
                // Cancel the order
                final result =
                    await OrderService.cancelOrder(order['orderDetailId']);

                // Clear any existing SnackBars before showing new ones
                ScaffoldMessenger.of(context).clearSnackBars();

                if (result.success) {
                  debugPrint(
                      '✅ Order deleted successfully, refreshing orders list...');

                  _runningOrdersBloc.add(RefreshOrders());

                  // Show success message first
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Order canceled successfully'),
                      backgroundColor: Colors.green,
                      duration: Duration(seconds: 3),
                    ),
                  );

                  // Add a delay to ensure the backend has processed the deletion
                  await Future.delayed(Duration(milliseconds: 1000));

                  // Force refresh the orders list multiple times to ensure update
                  debugPrint('🔄 Starting refresh sequence...');
                  _runningOrdersBloc.add(RefreshOrders());

                  // Add another refresh after a short delay as backup
                  Future.delayed(Duration(milliseconds: 1500), () {
                    debugPrint('🔄 Secondary refresh...');
                    _runningOrdersBloc.add(RefreshOrders());
                  });

                  debugPrint('🔄 RefreshOrders events added to bloc');
                } else {
                  // Show detailed error message from API
                  final errorMsg =
                      result.errorMessage ?? 'Failed to cancel order';
                  debugPrint('🚨 Showing error message: $errorMsg');

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        errorMsg,
                        style: TextStyle(fontSize: 14),
                      ),
                      backgroundColor: Colors.red,
                      duration: Duration(
                          seconds: 6), // Longer duration for error messages
                      behavior: SnackBarBehavior.floating,
                      margin: EdgeInsets.all(16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  );
                }
              } catch (e) {
                debugPrint('❌ Error canceling order: $e');

                // Clear any existing SnackBars before showing error
                ScaffoldMessenger.of(context).clearSnackBars();

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Error canceling order: ${e.toString()}',
                      style: TextStyle(fontSize: 14),
                    ),
                    backgroundColor: Colors.red,
                    duration: Duration(seconds: 6),
                    behavior: SnackBarBehavior.floating,
                    margin: EdgeInsets.all(16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                );
              }
            },
            child: Text(
              'Cancel Order',
              style: GoogleFonts.dmSans(),
            ),
          ),
        ],
      ),
    );
  }

  void _handleStatusChange(Map<String, dynamic> order, OrderStatus newStatus,
      BuildContext context) async {
    try {
      debugPrint(
          '🔄 Changing order ${order['orderDetailId']} status to ${newStatus.value}');

      final success = await OrderService.updateOrderStatus(
          order['orderDetailId'], newStatus.value);

      if (success) {
        // Refresh the orders list
        _runningOrdersBloc.add(RefreshOrders());

        // Close the modal
        Navigator.pop(context);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Order status updated to ${newStatus.displayName}'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update order status'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ Error updating order status: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error updating order status'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _handleStatusChangeFromCard(
      Map<String, dynamic> order, String newStatus, BuildContext context) {
    // Convert string status to OrderStatus enum
    OrderStatus? orderStatus = _convertStringToOrderStatus(newStatus);
    if (orderStatus != null) {
      // Show confirmation dialog
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text('Confirm Status Change'),
            content: Text(
                'Are you sure you want to change the order status to ${orderStatus.displayName}?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _handleStatusChange(order, orderStatus, context);
                },
                child: Text('Confirm'),
              ),
            ],
          );
        },
      );
    }
  }

  OrderStatus? _convertStringToOrderStatus(String status) {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return OrderStatus.PENDING;
      case 'IN_PREPARATION':
        return OrderStatus.IN_PREPARATION;
      case 'READY':
        return OrderStatus.READY;
      case 'SERVED':
        return OrderStatus.SERVED;
      case 'CANCELLED':
        return OrderStatus.CANCELLED;
      case 'CHECKOUT':
        return OrderStatus.CHECKOUT;
      case 'COMPLETED':
        return OrderStatus.COMPLETED;
      default:
        return null;
    }
  }

  void _handlePrintBill(
      Map<String, dynamic> order, BuildContext context) async {
    try {
      debugPrint('🖨️ Printing bill for order ${order['id']}');

      // Get the original OrderDetail from the stored data
      final originalOrderDetail = order['_originalOrderDetail'] as OrderDetail?;
      if (originalOrderDetail == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Original order data not available for printing',
              style: GoogleFonts.dmSans(),
            ),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Use the original OrderDetail and get the first bill
      final orderDetail = originalOrderDetail;
      final bill =
          orderDetail.bills.isNotEmpty ? orderDetail.bills.first : null;
      if (bill == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'No bill data available for this order',
              style: GoogleFonts.dmSans(),
            ),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Create thermal bill data
      final thermalBillData =
          ThermalBillData.fromOrderAndBill(orderDetail, bill);

      // Initialize thermal print service
      final thermalPrintService = ThermalPrintService();

      // Show options dialog
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: Colors.grey[900],
          title: Text(
            'Print Bill',
            style: GoogleFonts.dmSans(color: Colors.white),
          ),
          content: Text(
            'Choose an option for the bill:',
            style: GoogleFonts.dmSans(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Cancel',
                style: GoogleFonts.dmSans(color: Colors.white70),
              ),
            ),
            TextButton(
              onPressed: () async {
                Navigator.pop(context);
                await thermalPrintService.printBill(thermalBillData);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Bill sent to printer',
                      style: GoogleFonts.dmSans(),
                    ),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              child: Text(
                'Print',
                style: GoogleFonts.dmSans(color: Colors.green),
              ),
            ),
            TextButton(
              onPressed: () async {
                Navigator.pop(context);
                await thermalPrintService.shareAndSaveBill(thermalBillData);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Bill saved and shared',
                      style: GoogleFonts.dmSans(),
                    ),
                    backgroundColor: Colors.blue,
                  ),
                );
              },
              child: Text(
                'Share',
                style: GoogleFonts.dmSans(color: Colors.blue),
              ),
            ),
          ],
        ),
      );
    } catch (e) {
      debugPrint('❌ Error printing bill: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Error printing bill: ${e.toString()}',
            style: GoogleFonts.dmSans(),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _runningOrdersBloc,
      child: Scaffold(
        extendBodyBehindAppBar: true,
        key: _scaffoldKey,
        appBar: WaiterAppBar(scaffoldKey: _scaffoldKey),
        body: Stack(
          children: [
            TiledBackground(),
            BackdropFilter(
              filter: ui.ImageFilter.blur(sigmaX: 32, sigmaY: 32),
              child: BlocListener<RunningOrdersBloc, RunningOrdersState>(
                bloc: _runningOrdersBloc,
                listener: (context, state) {
                  debugPrint(
                      '🔄 RunningOrdersBloc state changed: ${state.status}');
                  debugPrint('🔄 Orders count: ${state.orders.length}');
                  if (state.status == RunningOrdersStatus.failure) {
                    debugPrint('❌ RunningOrdersBloc error: ${state.error}');
                  }
                },
                child: BlocBuilder<RunningOrdersBloc, RunningOrdersState>(
                  bloc: _runningOrdersBloc,
                  builder: (context, state) {
                    if (state.status == RunningOrdersStatus.loading) {
                      return Center(child: CircularProgressIndicator());
                    }

                    if (state.status == RunningOrdersStatus.failure) {
                      return Center(
                        child: Text(
                          state.error ?? 'An error occurred',
                          style: TextStyle(color: Colors.red),
                        ),
                      );
                    }

                    final filteredOrders = state.orders;

                    return Column(
                      children: [
                        SizedBox(height: AppBar().preferredSize.height + 30),
                        Container(
                          margin: EdgeInsets.symmetric(horizontal: 16),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(25),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          child: TabBar(
                            controller: _tabController,
                            dividerHeight: 0,
                            onTap: (index) {
                              debugPrint('🔄 Tab tapped: index $index');
                              String category = _getTabCategory(index);
                              debugPrint('🔄 Tab tapped: category $category');
                            },
                            indicator: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(25),
                            ),
                            labelStyle: GoogleFonts.dmSans(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                            unselectedLabelStyle: GoogleFonts.dmSans(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            labelColor: Colors.white,
                            unselectedLabelColor: Colors.white.withOpacity(0.5),
                            padding: EdgeInsets.all(4),
                            labelPadding: EdgeInsets.symmetric(horizontal: 4),
                            tabs: [
                              _buildTab("All", Icons.list_alt, context),
                              _buildTab("Dine In", Icons.restaurant, context),
                              _buildTab(
                                  "Takeaway", Icons.takeout_dining, context),
                              _buildTab(
                                  "Delivery", Icons.delivery_dining, context),
                            ],
                          ),
                        ),
                        SizedBox(
                            height: MediaQuery.of(context).size.height * 0.01),
                        // Stats summary with updated styling
                        Container(
                          margin: EdgeInsets.symmetric(horizontal: 16),
                          padding: EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.1),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              _buildStatItem(
                                "Total",
                                filteredOrders.length.toString(),
                                Colors.blue,
                                Icons.receipt_long,
                              ),
                              _buildStatItem(
                                "Ready",
                                filteredOrders
                                    .where(
                                        (order) => order["status"] == "Ready")
                                    .length
                                    .toString(),
                                Colors.green,
                                Icons.check_circle,
                              ),
                              _buildStatItem(
                                "In Progress",
                                filteredOrders
                                    .where((order) =>
                                        order["status"] == "In Progress")
                                    .length
                                    .toString(),
                                Colors.amber,
                                Icons.pending,
                              ),
                              _buildStatItem(
                                "Cooking",
                                filteredOrders
                                    .where(
                                        (order) => order["status"] == "Cooking")
                                    .length
                                    .toString(),
                                Colors.orange,
                                Icons.restaurant,
                              ),
                              _buildStatItem(
                                "Cancelled",
                                filteredOrders
                                    .where((order) =>
                                        order["status"] == "Cancelled")
                                    .length
                                    .toString(),
                                Colors.red,
                                Icons.cancel,
                              ),
                              _buildStatItem(
                                "Completed",
                                filteredOrders
                                    .where((order) =>
                                        order["status"] == "Completed")
                                    .length
                                    .toString(),
                                Colors.purple,
                                Icons.done_all,
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                            height: MediaQuery.of(context).size.height * 0.01),
                        Expanded(
                          child: filteredOrders.isEmpty
                              ? _buildEmptyState(
                                  state.selectedCategory, context)
                              : state.selectedCategory == "All"
                                  ? _buildGroupedOrdersList(
                                      filteredOrders, context)
                                  : _buildOrdersList(filteredOrders, context),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(String selectedCategory, BuildContext context) {
    String message;
    IconData icon;

    switch (selectedCategory) {
      case "All":
        message = "No orders found";
        icon = Icons.assignment_outlined;
        break;
      case "Dine In":
        message = "No dine-in orders found";
        icon = Icons.restaurant;
        break;
      case "Takeaway":
        message = "No takeaway orders found";
        icon = Icons.takeout_dining;
        break;
      case "Delivery":
        message = "No delivery orders found";
        icon = Icons.delivery_dining;
        break;
      default:
        message = "No orders found for this type";
        icon = Icons.assignment_outlined;
    }

    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            message,
            style: GoogleFonts.dmSans(
              fontSize: 18,
              color: Colors.grey[300],
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Text(
            selectedCategory == "All"
                ? "Try refreshing or check back later"
                : "Switch to 'All' to see orders of other types",
            style: GoogleFonts.dmSans(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          if (selectedCategory == "All") ...[
            SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                _runningOrdersBloc.add(RefreshOrders());
              },
              icon: Icon(Icons.refresh, size: 18),
              label: Text(
                'Refresh Orders',
                style: GoogleFonts.dmSans(fontSize: 14),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildGroupedOrdersList(
      List<Map<String, dynamic>> orders, BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    // Group orders by type
    final Map<String, List<Map<String, dynamic>>> groupedOrders = {
      'Dine In': [],
      'Takeaway': [],
      'Delivery': [],
    };

    for (var order in orders) {
      final type = order['type'] as String;
      groupedOrders[type]?.add(order);
    }

    // Remove empty categories
    groupedOrders.removeWhere((key, value) => value.isEmpty);

    if (isLandscape) {
      return ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: groupedOrders.length,
        itemBuilder: (context, index) {
          final category = groupedOrders.keys.elementAt(index);
          final categoryOrders = groupedOrders[category]!;

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding:
                    const EdgeInsets.only(left: 8.0, top: 16.0, bottom: 8.0),
                child: Row(
                  children: [
                    Icon(
                      _getCategoryIcon(category),
                      color: Colors.white70,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Text(
                      category,
                      style: GoogleFonts.dmSans(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(width: 8),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${categoryOrders.length}',
                        style: GoogleFonts.dmSans(
                          fontSize: 14,
                          color: Colors.white70,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              StaggeredGrid.count(
                crossAxisCount: 3,
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                children: categoryOrders.map((order) {
                  return InkWell(
                    onTap: () => _handleOrderTap(order, context),
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.08),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.1),
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: BackdropFilter(
                          filter: ui.ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                          child: RunningOrderCard(
                            order: order,
                            onAddItems: () => _handleAddItems(order, context),
                            onCancelOrder: () =>
                                _handleCancelOrder(order, context),
                            onPrintBill: () => _handlePrintBill(order, context),
                            onStatusChange: (newStatus) =>
                                _handleStatusChangeFromCard(
                                    order, newStatus, context),
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          );
        },
      );
    }

    // Portrait mode with grouped orders
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: groupedOrders.length,
      itemBuilder: (context, index) {
        final category = groupedOrders.keys.elementAt(index);
        final categoryOrders = groupedOrders[category]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 8.0, top: 16.0, bottom: 8.0),
              child: Row(
                children: [
                  Icon(
                    _getCategoryIcon(category),
                    color: Colors.white70,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Text(
                    category,
                    style: GoogleFonts.dmSans(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(width: 8),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${categoryOrders.length}',
                      style: GoogleFonts.dmSans(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            ...categoryOrders.map((order) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: InkWell(
                  onTap: () => _handleOrderTap(order, context),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.08),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.1),
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: BackdropFilter(
                        filter: ui.ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                        child: RunningOrderCard(
                          order: order,
                          onAddItems: () => _handleAddItems(order, context),
                          onCancelOrder: () =>
                              _handleCancelOrder(order, context),
                          onPrintBill: () => _handlePrintBill(order, context),
                          onStatusChange: (newStatus) =>
                              _handleStatusChangeFromCard(
                                  order, newStatus, context),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Dine In':
        return Icons.restaurant;
      case 'Takeaway':
        return Icons.takeout_dining;
      case 'Delivery':
        return Icons.delivery_dining;
      default:
        return Icons.list_alt;
    }
  }

  Widget _buildOrdersList(
      List<Map<String, dynamic>> orders, BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    if (isLandscape) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: StaggeredGrid.count(
          crossAxisCount: 3,
          mainAxisSpacing: 8,
          crossAxisSpacing: 8,
          children: orders.map((order) {
            return InkWell(
              onTap: () => _handleOrderTap(order, context),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.08),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.1),
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: BackdropFilter(
                    filter: ui.ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                    child: RunningOrderCard(
                      order: order,
                      onAddItems: () => _handleAddItems(order, context),
                      onCancelOrder: () => _handleCancelOrder(order, context),
                      onPrintBill: () => _handlePrintBill(order, context),
                      onStatusChange: (newStatus) =>
                          _handleStatusChangeFromCard(
                              order, newStatus, context),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      );
    }

    // Portrait mode - keep the original list view
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: orders.length,
      itemBuilder: (context, index) {
        final order = orders[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: InkWell(
            onTap: () => _handleOrderTap(order, context),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.08),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.white.withOpacity(0.1),
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: BackdropFilter(
                  filter: ui.ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: RunningOrderCard(
                    order: order,
                    onAddItems: () => _handleAddItems(order, context),
                    onCancelOrder: () => _handleCancelOrder(order, context),
                    onPrintBill: () => _handlePrintBill(order, context),
                    onStatusChange: (newStatus) =>
                        _handleStatusChangeFromCard(order, newStatus, context),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTab(String text, IconData icon, BuildContext context) {
    return Tab(
      height:
          MediaQuery.of(context).orientation == Orientation.portrait ? 48 : 36,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 50),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 18),
            SizedBox(width: 8),
            Text(text),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
      String label, String value, Color color, IconData icon) {
    return Row(
      children: [
        Container(
          margin: EdgeInsets.only(right: 16),
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: GoogleFonts.dmSans(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              label,
              style: GoogleFonts.dmSans(
                fontSize: 12,
                color: Colors.white.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

// Order Details Modal
class OrderDetailsModal extends StatefulWidget {
  final Map<String, dynamic> order;
  final VoidCallback? onRefresh;
  const OrderDetailsModal({super.key, required this.order, this.onRefresh});

  @override
  State<OrderDetailsModal> createState() => _OrderDetailsModalState();
}

class _OrderDetailsModalState extends State<OrderDetailsModal> {
  String? selectedPaymentMethod;
  double tipAmount = 0.0;
  final tipController = TextEditingController();
  bool isProcessingPayment = false;
  double cashAmount = 0.0;
  double cardAmount = 0.0;
  bool isSplittingBill = false;
  int numberOfBills = 2; // Default to 2 bills
  List<double> billAmounts = <double>[
    0.0,
    0.0
  ]; // Dynamic list for bill amounts
  Map<String, int> billAssignments =
      {}; // 0 for unassigned, 1+ for bill numbers
  List<double> billSubtotals = <double>[
    0.0,
    0.0
  ]; // Dynamic list for bill subtotals

  // Promotion functionality - now handled by PromotionService
  late PromotionState _promotionState;
  final TextEditingController _couponController = TextEditingController();
  final TextEditingController _tipCodeController = TextEditingController();
  final TextEditingController _voucherController = TextEditingController();

  // Cash payment functionality
  final cashHandedController = TextEditingController();
  double cashHandedAmount = 0.0;

  @override
  void initState() {
    super.initState();
    _promotionState = PromotionState(); // Initialize promotion state
  }

  @override
  void dispose() {
    _couponController.dispose();
    _tipCodeController.dispose();
    _voucherController.dispose();
    tipController.dispose();
    cashHandedController.dispose();
    super.dispose();
  }

  void _updateBillTotals() {
    // Reset all bill subtotals to match current number of bills
    billSubtotals = List<double>.filled(numberOfBills, 0.0);

    final itemsToUse = _getAllItemsForCalculation();
    itemsToUse.forEach((item) {
      // Use the final calculated price from backend (already includes all calculations)
      final price = _getItemPrice(item);
      final quantity = (item['quantity'] ?? 1).toInt();
      final itemTotal = price * quantity;
      final billIndex = billAssignments[item['id']];
      if (billIndex != null && billIndex > 0 && billIndex <= numberOfBills) {
        billSubtotals[billIndex - 1] += itemTotal;
      }
    });

    // Use backend calculated values for proportional split
    final backendTotal = _getTotalAmount();
    final totalItemsValue = billSubtotals.fold(0.0, (sum, subtotal) => sum + subtotal);

    billAmounts = List<double>.generate(numberOfBills, (index) {
      if (totalItemsValue > 0) {
        // Calculate proportional amount based on backend total
        final proportion = billSubtotals[index] / totalItemsValue;
        return backendTotal * proportion;
      }
      return 0.0;
    });

    setState(() {});
  }

  /// Apply coupon to the order using new PromotionService API
  Future<void> _applyCoupon() async {
    final couponCode = _couponController.text.trim();

    if (couponCode.isEmpty) {
      _showErrorSnackBar('Please enter a coupon code');
      return;
    }

    // Check if already applied
    if (_promotionState.isCouponApplied(couponCode)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Coupon already applied'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _promotionState = _promotionState.copyWith(isApplyingCoupon: true);
    });

    final orderDetailId = widget.order['orderDetailId'] as String?;
    if (orderDetailId == null) {
      _showErrorSnackBar('Order detail ID not found');
      setState(() {
        _promotionState = _promotionState.copyWith(isApplyingCoupon: false);
      });
      return;
    }

    try {
      // Call the new redeem coupons API
      final result = await PromotionService.redeemCoupons(
        orderDetailId: orderDetailId,
        coupons: [couponCode],
      );

      setState(() {
        _promotionState = _promotionState.copyWith(isApplyingCoupon: false);
      });

      if (result != null) {
        // Coupon redemption initiated - show OTP modal
        _showEnhancedOTPModal('coupon', couponCode, orderDetailId);
      } else {
        _showErrorSnackBar('Failed to redeem coupon. Please check the code and try again.');
      }
    } catch (e) {
      setState(() {
        _promotionState = _promotionState.copyWith(isApplyingCoupon: false);
      });
      _showErrorSnackBar('Error redeeming coupon: $e');
    }
  }

  /// Apply tip to the order using PromotionService
  Future<void> _applyTip() async {
    final tipCode = _tipCodeController.text.trim();

    // Check if already applied
    if (_promotionState.isTipApplied(tipCode)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Tip already applied'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _promotionState = _promotionState.copyWith(isApplyingTip: true);
    });

    final orderDetailId = widget.order['orderDetailId'] as String?;
    if (orderDetailId == null) {
      _showErrorSnackBar('Order detail ID not found');
      setState(() {
        _promotionState = _promotionState.copyWith(isApplyingTip: false);
      });
      return;
    }

    final result = await PromotionService.applyTip(
      orderDetailId: orderDetailId,
      tipCode: tipCode,
    );

    setState(() {
      _promotionState = _promotionState.copyWith(isApplyingTip: false);
    });

    if (result.success) {
      setState(() {
        _promotionState = _promotionState.addAppliedTip(tipCode);
      });
      _tipCodeController.clear();
      _showSuccessSnackBar(result.message);
    } else {
      _showErrorSnackBar(result.message);
    }
  }

  /// Apply voucher to the order using new PromotionService API
  Future<void> _applyVoucher() async {
    final voucherCode = _voucherController.text.trim();

    if (voucherCode.isEmpty) {
      _showErrorSnackBar('Please enter a voucher code');
      return;
    }

    // Check if already applied
    if (_promotionState.isVoucherApplied(voucherCode)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Voucher already applied'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _promotionState = _promotionState.copyWith(isApplyingVoucher: true);
    });

    final orderDetailId = widget.order['orderDetailId'] as String?;
    if (orderDetailId == null) {
      _showErrorSnackBar('Order detail ID not found');
      setState(() {
        _promotionState = _promotionState.copyWith(isApplyingVoucher: false);
      });
      return;
    }

    try {
      // Call the new redeem vouchers API
      final result = await PromotionService.redeemVouchers(
        orderDetailId: orderDetailId,
        vouchers: [voucherCode],
      );

      setState(() {
        _promotionState = _promotionState.copyWith(isApplyingVoucher: false);
      });

      if (result != null) {
        // Voucher redemption initiated - show OTP modal
        _showEnhancedOTPModal('voucher', voucherCode, orderDetailId);
      } else {
        _showErrorSnackBar('Failed to redeem voucher. Please check the code and try again.');
      }
    } catch (e) {
      setState(() {
        _promotionState = _promotionState.copyWith(isApplyingVoucher: false);
      });
      _showErrorSnackBar('Error redeeming voucher: $e');
    }
  }

  /// Show enhanced OTP modal for coupon/voucher confirmation
  void _showEnhancedOTPModal(String promotionType, String promotionCode, String orderDetailId) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => EnhancedOtpVerificationModal(
        orderDetailId: orderDetailId,
        promotionType: promotionType,
        promotionCode: promotionCode,
        onComplete: (success, message) {
          if (success) {
            // Update promotion state
            setState(() {
              if (promotionType == 'coupon') {
                _promotionState = _promotionState.addAppliedCoupon(promotionCode);
                _couponController.clear();
              } else if (promotionType == 'voucher') {
                _promotionState = _promotionState.addAppliedVoucher(promotionCode);
                _voucherController.clear();
              }
            });

            _showSuccessSnackBar(message ?? '$promotionType applied successfully!');

            // Refresh the order to show updated totals
            final runningOrdersPage = context.findAncestorStateOfType<_RunningOrdersPageState>();
            runningOrdersPage?._runningOrdersBloc.add(RefreshOrders());
          } else {
            _showErrorSnackBar(message ?? 'Failed to verify OTP');
          }
        },
      ),
    );
  }

  /// Add tip to the order using TipService
  Future<void> _addTip() async {
    final billData = _getBillData();
    if (billData == null) {
      _showErrorSnackBar('Bill information not found');
      return;
    }

    final billId = billData['billId'] as String?;
    if (billId == null) {
      _showErrorSnackBar('Bill ID not found');
      return;
    }

    // Show tip amount dialog
    _showTipAmountDialog(billId);
  }

  /// Show tip amount selection dialog
  void _showTipAmountDialog(String billId) {
    final TextEditingController tipController = TextEditingController();
    final billTotal = _getTotalAmount();
    final suggestedAmounts = TipService.getSuggestedTipAmounts(billTotal);
    double? selectedTipAmount;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          backgroundColor: Colors.grey[900],
          title: Text(
            'Add Tip',
            style: GoogleFonts.dmSans(color: Colors.white),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Bill Total: ${CurrencyFormatter.format(billTotal)}',
                style: GoogleFonts.dmSans(
                  color: Colors.grey[400],
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 16),

              // Suggested tip amounts
              Text(
                'Quick Select:',
                style: GoogleFonts.dmSans(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: TipService.getSuggestedTipPercentages().asMap().entries.map((entry) {
                  final percentage = entry.value;
                  final amount = suggestedAmounts[entry.key];
                  return ChoiceChip(
                    label: Text('$percentage% (${CurrencyFormatter.format(amount)})'),
                    selected: selectedTipAmount == amount,
                    onSelected: (selected) {
                      setDialogState(() {
                        selectedTipAmount = selected ? amount : null;
                        tipController.text = selected ? amount.toStringAsFixed(2) : '';
                      });
                    },
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),

              // Custom amount input
              TextField(
                controller: tipController,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                style: GoogleFonts.dmSans(color: Colors.white),
                decoration: InputDecoration(
                  labelText: 'Custom Amount',
                  labelStyle: GoogleFonts.dmSans(color: Colors.grey[400]),
                  prefixText: '₹ ',
                  prefixStyle: GoogleFonts.dmSans(color: Colors.white),
                  border: OutlineInputBorder(),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey[600]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.blue),
                  ),
                ),
                onChanged: (value) {
                  setDialogState(() {
                    selectedTipAmount = double.tryParse(value);
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Cancel',
                style: GoogleFonts.dmSans(color: Colors.grey[400]),
              ),
            ),
            ElevatedButton(
              onPressed: selectedTipAmount != null && selectedTipAmount! > 0
                  ? () async {
                      Navigator.pop(context);
                      await _processTip(billId, selectedTipAmount!);
                    }
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
              ),
              child: Text(
                'Add Tip',
                style: GoogleFonts.dmSans(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Process tip addition
  Future<void> _processTip(String billId, double tipAmount) async {
    if (!TipService.isValidTipAmount(tipAmount)) {
      _showErrorSnackBar('Invalid tip amount');
      return;
    }

    try {
      final success = await TipService.addTip(
        billId: billId,
        tipAmount: tipAmount,
      );

      if (success) {
        _showSuccessSnackBar('Tip of ${TipService.formatTipAmount(tipAmount)} added successfully!');

        // Refresh the order to show updated totals
        final runningOrdersPage = context.findAncestorStateOfType<_RunningOrdersPageState>();
        runningOrdersPage?._runningOrdersBloc.add(RefreshOrders());
      } else {
        _showErrorSnackBar('Failed to add tip. Please try again.');
      }
    } catch (e) {
      _showErrorSnackBar('Error adding tip: $e');
    }
  }

  /// Helper method to show success snackbar
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// Helper method to show error snackbar
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  /// Helper method to safely get item price as double (final calculated price from backend)
  double _getItemPrice(Map<String, dynamic> item) {
    debugPrint(item.toString());
    final price = item['price'];
    if (price is String) {
      return double.tryParse(price) ?? 0.0;
    } else if (price is num) {
      return price.toDouble();
    }
    return 0.0;
  }

  /// Helper method to get base item price from baseItem
  double _getItemBase(Map<String, dynamic> item) {
    print('=== DEBUG _getItemBase ===');
    print('Full item data: $item');
    print('Item keys: ${item.keys.toList()}');
    print('BaseItem exists: ${item.containsKey('baseItem')}');
    print('BaseItem value: ${item['baseItem']}');

    // First try to get from baseItem structure
    final baseItem = item['baseItem'] as Map<String, dynamic>?;
    if (baseItem != null) {
      print('BaseItem found: $baseItem');
      final basePrice = baseItem['price'];
      print('Base price from baseItem: $basePrice');
      if (basePrice is String) {
        final parsed = double.tryParse(basePrice) ?? 0.0;
        print('Returning parsed base price: $parsed');
        return parsed;
      } else if (basePrice is num) {
        final converted = basePrice.toDouble();
        print('Returning converted base price: $converted');
        return converted;
      }
    }

    // Try alternative structure - sometimes basePrice might be directly in item
    final basePrice = item['basePrice'];
    if (basePrice != null) {
      print('Direct basePrice found: $basePrice');
      if (basePrice is String) {
        return double.tryParse(basePrice) ?? 0.0;
      } else if (basePrice is num) {
        return basePrice.toDouble();
      }
    }

    // Fallback to regular price if baseItem is not available
    print('Using fallback to regular price');
    final fallback = _getItemPrice(item);
    print('Fallback price: $fallback');
    print('=== END DEBUG ===');
    return fallback;
  }

  /// Get bill data from order (backend calculated)
  Map<String, dynamic>? _getBillData() {
    final bills = widget.order['bills'] as List<dynamic>?;
    if (bills != null && bills.isNotEmpty) {
      return bills.first as Map<String, dynamic>;
    }
    return null;
  }

  /// Get subtotal from backend bill data
  double _getSubtotal() {
    final billData = _getBillData();
    if (billData != null) {
      final subtotal = billData['subtotal'];
      if (subtotal is String) {
        return double.tryParse(subtotal) ?? 0.0;
      } else if (subtotal is num) {
        return subtotal.toDouble();
      }
    }
    return 0.0;
  }

  /// Get total tax from backend bill data
  double _getTotalTax() {
    final billData = _getBillData();
    if (billData != null) {
      final totalTax = billData['totalTax'];
      if (totalTax is String) {
        return double.tryParse(totalTax) ?? 0.0;
      } else if (totalTax is num) {
        return totalTax.toDouble();
      }
    }
    return 0.0;
  }

  /// Get total amount from backend bill data
  double _getTotalAmount() {
    final billData = _getBillData();
    if (billData != null) {
      final totalAmount = billData['totalAmount'];
      if (totalAmount is String) {
        return double.tryParse(totalAmount) ?? 0.0;
      } else if (totalAmount is num) {
        return totalAmount.toDouble();
      }
    }
    return 0.0;
  }

  /// Get tip amount from backend bill data
  double _getTipAmount() {
    final billData = _getBillData();
    if (billData != null) {
      final tipAmount = billData['tipAmount'];
      if (tipAmount is String) {
        return double.tryParse(tipAmount) ?? 0.0;
      } else if (tipAmount is num) {
        return tipAmount.toDouble();
      }
    }
    return 0.0;
  }

  /// Get total discount from backend bill data
  double _getTotalDiscount() {
    final billData = _getBillData();
    if (billData != null) {
      final totalDiscount = billData['totalDiscount'];
      if (totalDiscount is String) {
        return double.tryParse(totalDiscount) ?? 0.0;
      } else if (totalDiscount is num) {
        return totalDiscount.toDouble();
      }
    }
    return 0.0;
  }

  /// Get applied discounts/coupons from backend bill data
  List<Map<String, dynamic>> _getAppliedDiscounts() {
    final billData = _getBillData();
    if (billData != null) {
      final discounts = billData['discounts'] as List<dynamic>?;
      if (discounts != null) {
        return discounts.map((discount) => Map<String, dynamic>.from(discount)).toList();
      }
    }
    return [];
  }

  /// Get tax breakdown from backend bill data
  List<Map<String, dynamic>> _getTaxBreakdown() {
    final billData = _getBillData();
    if (billData != null) {
      final taxBreakdown = billData['taxBreakdown'] as List<dynamic>?;
      if (taxBreakdown != null) {
        return taxBreakdown.map((tax) => Map<String, dynamic>.from(tax)).toList();
      }
    }
    return [];
  }

  /// Helper method to determine which items to display based on order status (excluding misc items)
  List<Map<String, dynamic>> _getItemsToDisplay() {
    final status = (widget.order['status'] ?? '').toString().toUpperCase();

    // For CHECKOUT status, use regular items
    if (status == 'CHECKOUT') {
      return List<Map<String, dynamic>>.from(widget.order['items'] as List? ?? []);
    }

    // For other statuses, use queue items if available
    final queueItems = widget.order['queueItems'] as List<dynamic>?;
    if (queueItems != null && queueItems.isNotEmpty) {
      print('=== DEBUG _getItemsToDisplay ===');
      print('Processing queue items...');
      final result = queueItems
          .expand((queueItem) => queueItem['orderItems'] as List<dynamic>? ?? [])
          .map<Map<String, dynamic>>((item) {
            print('Original item: $item');
            print('Original item has baseItem: ${item.containsKey('baseItem')}');
            final itemMap = Map<String, dynamic>.from(item);
            // Ensure we have an 'id' field for the UI (convert orderItemId to id)
            if (itemMap['orderItemId'] != null && itemMap['id'] == null) {
              itemMap['id'] = itemMap['orderItemId'];
            }
            print('Transformed item: $itemMap');
            print('Transformed item has baseItem: ${itemMap.containsKey('baseItem')}');
            // Preserve all original fields including baseItem
            return itemMap;
          })
          .toList();
      print('=== END DEBUG _getItemsToDisplay ===');
      return result;
    }

    // Fallback to regular items
    final regularItems = widget.order['items'] as List? ?? [];
    return regularItems.map<Map<String, dynamic>>((item) {
      final itemMap = Map<String, dynamic>.from(item);
      // Ensure we have an 'id' field for the UI (convert orderItemId to id)
      if (itemMap['orderItemId'] != null && itemMap['id'] == null) {
        itemMap['id'] = itemMap['orderItemId'];
      }
      // Preserve all original fields including baseItem
      return itemMap;
    }).toList();
  }

  /// Helper method to get all items including miscellaneous items for calculations
  List<Map<String, dynamic>> _getAllItemsForCalculation() {
    final regularItems = _getItemsToDisplay();
    final miscItems = widget.order['miscItems'] as List<dynamic>? ?? [];

    final miscItemsFormatted = miscItems.map((miscItem) => {
      'id': miscItem['id'] ?? 'misc_${miscItems.indexOf(miscItem)}',
      'name': miscItem['name'] ?? 'Unknown Item',
      'price': (miscItem['price'] ?? 0).toDouble(),
      'quantity': 1,
      'customization': null,
    }).toList();

    return [...regularItems, ...miscItemsFormatted];
  }

  /// Show OTP verification modal using the new widget
  void _showOTPModal() {
    if (_promotionState.pendingCode == null || _promotionState.pendingType == null) {
      return;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => OTPVerificationModal(
        code: _promotionState.pendingCode!,
        type: _promotionState.pendingType!,
        isVerifying: _promotionState.isVerifyingOTP,
        onVerify: _verifyOTP,
        onCancel: () {
          setState(() {
            _promotionState = _promotionState.clearPendingOTP();
          });
          Navigator.pop(context);
        },
      ),
    );
  }

  /// Verify OTP using PromotionService
  Future<void> _verifyOTP(String otp) async {
    if (_promotionState.pendingCode == null || _promotionState.pendingType == null) {
      return;
    }

    setState(() {
      _promotionState = _promotionState.copyWith(isVerifyingOTP: true);
    });

    final orderDetailId = widget.order['orderDetailId'] as String?;
    if (orderDetailId == null) {
      _showErrorSnackBar('Order detail ID not found');
      setState(() {
        _promotionState = _promotionState.copyWith(isVerifyingOTP: false);
      });
      return;
    }

    final result = await PromotionService.verifyOTP(
      orderDetailId: orderDetailId,
      code: _promotionState.pendingCode!,
      type: _promotionState.pendingType!,
      otp: otp,
    );

    setState(() {
      _promotionState = _promotionState.copyWith(isVerifyingOTP: false);
    });

    if (result.success) {
      // Update the applied codes based on type
      if (result.type == 'coupon') {
        setState(() {
          _promotionState = _promotionState.addAppliedCoupon(result.code!);
        });
        _couponController.clear();
      } else if (result.type == 'voucher') {
        setState(() {
          _promotionState = _promotionState.addAppliedVoucher(result.code!);
        });
        _voucherController.clear();
      }

      // Clear pending state and close modal
      setState(() {
        _promotionState = _promotionState.clearPendingOTP();
      });
      Navigator.pop(context);
      _showSuccessSnackBar(result.message);
    } else {
      _showErrorSnackBar(result.message);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    // Use backend calculated values instead of frontend calculations
    final subtotal = _getSubtotal();
    final tax = _getTotalTax();
    final total = _getTotalAmount();
    final backendTipAmount = _getTipAmount();
    final totalDiscount = _getTotalDiscount();

    if (isLandscape) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Left side - Order details and items
          Expanded(
            flex: 3,
            child: CustomScrollView(
              slivers: [
                SliverPadding(
                  padding: EdgeInsets.all(20),
                  sliver: SliverToBoxAdapter(
                    child: _buildOrderHeader(),
                  ),
                ),
                SliverPadding(
                  padding: EdgeInsets.symmetric(horizontal: 20),
                  sliver: SliverToBoxAdapter(
                    child: _buildItemsList(),
                  ),
                ),
                SliverPadding(
                  padding: EdgeInsets.only(bottom: 20),
                ),
              ],
            ),
          ),

          // Vertical divider
          Container(
            width: 1,
            margin: EdgeInsets.symmetric(vertical: 20),
            color: Colors.grey[800],
          ),

          // Right side - Payment and actions
          Expanded(
            flex: 2,
            child: CustomScrollView(
              slivers: [
                SliverPadding(
                  padding: EdgeInsets.all(20),
                  sliver: SliverList(
                    delegate: SliverChildListDelegate([
                      _buildMainTabSection(subtotal, tax, total, totalDiscount, backendTipAmount),
                      SizedBox(height: 1.h),
                      _buildActionButtons(total),
                    ]),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    // Portrait layout
    return CustomScrollView(
      slivers: [
        SliverPadding(
          padding: EdgeInsets.all(20),
          sliver: SliverList(
            delegate: SliverChildListDelegate([
              _buildOrderHeader(),
              SizedBox(height: 1.h),
              _buildItemsList(),
              SizedBox(height: 1.h),
              _buildMainTabSection(subtotal, tax, total, totalDiscount, backendTipAmount),
              SizedBox(height: 1.h),
              _buildActionButtons(total),
              // Add bottom padding for safe area
              SizedBox(height: MediaQuery.of(context).padding.bottom),
            ]),
          ),
        ),
      ],
    );
  }

  Widget _buildOrderHeader() {
    final orderid = widget.order['id'];
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  orderid,
                  style: GoogleFonts.dmSans(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  "${widget.order['type']} • Table ${widget.order['table'].toString().split(',').last.replaceAll(')', '')}",
                  style: GoogleFonts.dmSans(
                    color: Colors.grey[400],
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: _getStatusColor(widget.order['status']),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                widget.order['status'],
                style: GoogleFonts.dmSans(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        // _buildStatusChangeSection(),
      ],
    );
  }

  // Widget _buildStatusChangeSection() {
  //   final currentStatus =
  //       dropdown.OrderStatus.fromString(widget.order['status']);
  //
  //   return Container(
  //     padding: EdgeInsets.all(16),
  //     decoration: BoxDecoration(
  //       color: Colors.grey[800],
  //       borderRadius: BorderRadius.circular(12),
  //       border: Border.all(color: Colors.grey[700]!),
  //     ),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Text(
  //           'Change Order Status',
  //           style: GoogleFonts.dmSans(
  //             color: Colors.white,
  //             fontSize: 16,
  //             fontWeight: FontWeight.bold,
  //           ),
  //         ),
  //         SizedBox(height: 12),
  //         dropdown.OrderStatusDropdown(
  //           currentStatus: currentStatus,
  //           onStatusChanged: (newStatus) {
  //             final runningOrdersPage =
  //                 context.findAncestorStateOfType<_RunningOrdersPageState>();
  //             runningOrdersPage?._handleStatusChange(
  //                 widget.order, _convertToLegacyStatus(newStatus), context);
  //           },
  //         ),
  //       ],
  //     ),
  //   );
  // }

  // Convert new dropdown status to legacy status for compatibility
  OrderStatus _convertToLegacyStatus(dropdown.OrderStatus dropdownStatus) {
    switch (dropdownStatus) {
      case dropdown.OrderStatus.PENDING:
        return OrderStatus.PENDING;
      case dropdown.OrderStatus.IN_PREPARATION:
        return OrderStatus.IN_PREPARATION;
      case dropdown.OrderStatus.READY:
        return OrderStatus.READY;
      case dropdown.OrderStatus.SERVED:
        return OrderStatus.SERVED;
      case dropdown.OrderStatus.CANCELLED:
        return OrderStatus.CANCELLED;
      case dropdown.OrderStatus.CHECKOUT:
        return OrderStatus.CHECKOUT;
      case dropdown.OrderStatus.COMPLETED:
        return OrderStatus.COMPLETED;
    }
  }

  void _showStatusChangeConfirmation(OrderStatus newStatus) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          'Change Order Status',
          style: GoogleFonts.dmSans(color: Colors.white),
        ),
        content: Text(
          'Change order status to ${newStatus.displayName}?',
          style: GoogleFonts.dmSans(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.dmSans(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: newStatus.color,
            ),
            onPressed: () {
              Navigator.pop(context);
              final runningOrdersPage =
                  context.findAncestorStateOfType<_RunningOrdersPageState>();
              runningOrdersPage?._handleStatusChange(
                  widget.order, newStatus, context);
            },
            child: Text(
              'Confirm',
              style: GoogleFonts.dmSans(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemsList() {
    final status = (widget.order['status'] ?? '').toString().toUpperCase();
    final queueItems = widget.order['queueItems'] as List<dynamic>?;
    final miscItems = widget.order['miscItems'] as List<dynamic>? ?? [];
    final alerts = widget.order['alerts'] as List<dynamic>? ?? [];
    final notes = widget.order['notes'] as String? ?? '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Show alerts if any
        if (alerts.isNotEmpty) ...[
          Container(
            padding: EdgeInsets.all(12),
            margin: EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.warning, color: Colors.red, size: 20),
                    SizedBox(width: 8),
                    Text(
                      'Alerts',
                      style: GoogleFonts.dmSans(
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8),
                ...alerts.map((alert) => Padding(
                  padding: EdgeInsets.only(bottom: 4),
                  child: Text(
                    '• ${alert.toString()}',
                    style: GoogleFonts.dmSans(
                      color: Colors.red[300],
                      fontSize: 14,
                    ),
                  ),
                )).toList(),
              ],
            ),
          ),
        ],

        // Show notes if any
        if (notes.isNotEmpty) ...[
          Container(
            padding: EdgeInsets.all(12),
            margin: EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.note, color: Colors.blue, size: 20),
                    SizedBox(width: 8),
                    Text(
                      'Notes',
                      style: GoogleFonts.dmSans(
                        color: Colors.blue,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8),
                Text(
                  notes,
                  style: GoogleFonts.dmSans(
                    color: Colors.blue[300],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],


        if (isSplittingBill) ...[
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16),
            child: Row(
              children: [
                Text(
                  'Split Bills ($numberOfBills)',
                  style: GoogleFonts.dmSans(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(width: 12),
                if (numberOfBills < 5)
                  IconButton(
                    icon: Icon(Icons.add_circle, color: Colors.green),
                    onPressed: () {
                      setState(() {
                        numberOfBills++;
                        // Recreate the lists with the new size
                        billAmounts = List<double>.filled(numberOfBills, 0.0);
                        billSubtotals = List<double>.filled(numberOfBills, 0.0);
                        _updateBillTotals();
                      });
                    },
                    tooltip: 'Add Bill',
                  ),
                if (numberOfBills > 2)
                  IconButton(
                    icon: Icon(Icons.remove_circle, color: Colors.red),
                    onPressed: () {
                      setState(() {
                        // Remove assignments for the last bill
                        billAssignments.removeWhere(
                            (key, value) => value == numberOfBills);
                        numberOfBills--;
                        // Recreate the lists with the new size
                        billAmounts = List<double>.filled(numberOfBills, 0.0);
                        billSubtotals = List<double>.filled(numberOfBills, 0.0);
                        _updateBillTotals();
                      });
                    },
                    tooltip: 'Remove Bill',
                  ),
                Spacer(),
                TextButton.icon(
                  icon: Icon(Icons.close, color: Colors.red),
                  label: Text(
                    'Cancel Split',
                    style: GoogleFonts.dmSans(color: Colors.red),
                  ),
                  onPressed: () {
                    setState(() {
                      isSplittingBill = false;
                      numberOfBills = 2;
                      billAmounts = <double>[0.0, 0.0];
                      billSubtotals = <double>[0.0, 0.0];
                      billAssignments.clear();
                      _updateBillTotals();
                    });
                  },
                ),
              ],
            ),
          ),
          StaggeredGrid.count(
            crossAxisCount: numberOfBills > 3 ? 3 : numberOfBills,
            mainAxisSpacing: 12,
            crossAxisSpacing: 12,
            children: List.generate(numberOfBills, (index) {
              final billColors = [
                Colors.blue,
                Colors.green,
                Colors.orange,
                Colors.purple,
                Colors.teal,
              ];
              final color = billColors[index % billColors.length];

              return Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: color.withOpacity(0.5)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Bill ${index + 1}',
                      style: GoogleFonts.dmSans(
                        color: color,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      CurrencyFormatter.format(billSubtotals[index]),
                      style: GoogleFonts.dmSans(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ),
          SizedBox(height: 16),
        ],
        // Display queue items or regular items based on status
        if (status != 'CHECKOUT' && queueItems != null && queueItems.isNotEmpty) ...[
          // Show queue items grouped by queue position
          ...queueItems.asMap().entries.map((entry) {
            final queueIndex = entry.key;
            final queueItem = entry.value;
            final queueOrderItems = queueItem['orderItems'] as List<dynamic>? ?? [];

            return Container(
              margin: EdgeInsets.only(bottom: 16),
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.queue, color: Colors.green, size: 20),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Queue ${queueIndex + 1}',
                          style: GoogleFonts.dmSans(
                            color: Colors.green,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ),


                        SizedBox(width: 8),
                        Row(
                          children: [
                            // Current status display
                            // Builder(
                            //   builder: (context) {
                            //     final statusInfo = _getQueueStatusInfo(queueItem['status'] ?? 'PENDING');
                            //     return Container(
                            //       padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            //       decoration: BoxDecoration(
                            //         color: (statusInfo['color'] as Color).withOpacity(0.2),
                            //         borderRadius: BorderRadius.circular(12),
                            //       ),
                            //       child: Row(
                            //         mainAxisSize: MainAxisSize.min,
                            //         children: [
                            //           Icon(
                            //             statusInfo['icon'] as IconData,
                            //             color: statusInfo['color'] as Color,
                            //             size: 16,
                            //           ),
                            //           SizedBox(width: 4),
                            //           Text(
                            //             statusInfo['label'] as String,
                            //             style: GoogleFonts.dmSans(
                            //               color: statusInfo['color'] as Color,
                            //               fontSize: 12,
                            //               fontWeight: FontWeight.w500,
                            //             ),
                            //           ),
                            //         ],
                            //       ),
                            //     );
                            //   },
                            // ),
                            SizedBox(width: 12),
                            // Status change dropdown
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 1.w, vertical: 0.1.h),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.white.withOpacity(0.3)),
                              ),
                              child: DropdownButtonHideUnderline(
                                child: DropdownButton<String>(
                                  value: queueItem['status'] ?? 'PENDING',
                                  icon: Icon(Icons.arrow_drop_down, color: Colors.white, size: 20),
                                  dropdownColor: Colors.grey[800],
                                  style: GoogleFonts.dmSans(
                                    color: Colors.white,
                                    fontSize: 12,
                                  ),
                                  items: _getStatusOptions().map((option) {
                                    return DropdownMenuItem<String>(
                                      value: option['value'],
                                      child: Text(
                                        option['label']!,
                                        style: GoogleFonts.dmSans(
                                          color: Colors.white,
                                          fontSize: 12,
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                  onChanged: (String? newStatus) {
                                    if (newStatus != null && newStatus != queueItem['status']) {
                                      _updateQueueStatus(queueItem['orderQueueId'], newStatus);
                                    }
                                    Navigator.pop(context);
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                  ),
                  SizedBox(height: 8),
                  // Queue status and dropdown

                  SizedBox(height: 12),
                  ...queueOrderItems.map((item) => _buildItemRow(item, queueIndex)).toList(),
                ],
              ),
            );
          }).toList(),
        ] else ...[
          // Show regular items for CHECKOUT status or when no queue items
          ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.4,
            ),
            child: ListView.builder(
              shrinkWrap: true,
              scrollDirection: Axis.vertical,
              physics: AlwaysScrollableScrollPhysics(),
              itemCount: _getItemsToDisplay().length + miscItems.length,
              itemBuilder: (context, index) {
                final regularItemsCount = _getItemsToDisplay().length;

                if (index < regularItemsCount) {
                  // Show regular items
                  final item = _getItemsToDisplay()[index];
                  return _buildItemRow(item, null);
                } else {
                  // Show miscellaneous items
                  final miscIndex = index - regularItemsCount;
                  final miscItem = miscItems[miscIndex];
                  final miscItemFormatted = {
                    'id': miscItem['id'] ?? 'misc_${miscIndex}',
                    'name': miscItem['name'] ?? 'Unknown Item',
                    'price': (miscItem['price'] ?? 0).toDouble(),
                    'quantity': 1,
                    'customization': null,
                  };
                  return _buildItemRow(miscItemFormatted, null);
                }
              },
            ),
          ),
        ],

        // Show miscellaneous items at the end for queue-based orders too
        if (status != 'CHECKOUT' && queueItems != null && queueItems.isNotEmpty && miscItems.isNotEmpty) ...[
          SizedBox(height: 16),
          Text(
            'Additional Items',
            style: GoogleFonts.dmSans(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
          SizedBox(height: 8),
          ...miscItems.map((miscItem) {
            final miscItemFormatted = {
              'id': miscItem['id'] ?? 'misc_${miscItems.indexOf(miscItem)}',
              'name': miscItem['name'] ?? 'Unknown Item',
              'price': (miscItem['price'] ?? 0).toDouble(),
              'quantity': 1,
              'customization': null,
            };
            return _buildItemRow(miscItemFormatted, null);
          }).toList(),
        ],
      ],
    );
  }

  /// Helper method to get display name for queue position
  String _getQueueDisplayName(int position) {
    switch (position) {
      case 1:
        return 'First Ordered Items';
      case 2:
        return 'Second Ordered Items';
      case 3:
        return 'Third Ordered Items';
      case 4:
        return 'Fourth Ordered Items';
      case 5:
        return 'Fifth Ordered Items';
      default:
        return '${position}th Ordered Items';
    }
  }

  /// Helper method to get queue status display info
  Map<String, dynamic> _getQueueStatusInfo(String status) {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return {
          'color': Colors.orange,
          'icon': Icons.pending,
          'label': 'Pending',
        };
      case 'IN_PREPARATION':
        return {
          'color': Colors.blue,
          'icon': Icons.restaurant,
          'label': 'In Preparation',
        };
      case 'READY':
        return {
          'color': Colors.green,
          'icon': Icons.check_circle,
          'label': 'Ready',
        };
      case 'SERVED':
        return {
          'color': Colors.grey,
          'icon': Icons.done_all,
          'label': 'Served',
        };
      default:
        return {
          'color': Colors.grey,
          'icon': Icons.help,
          'label': status,
        };
    }
  }

  /// Helper method to get available status options for dropdown
  List<Map<String, String>> _getStatusOptions() {
    return [
      {'value': 'PENDING', 'label': 'Pending'},
      {'value': 'IN_PREPARATION', 'label': 'In Preparation'},
      {'value': 'READY', 'label': 'Ready'},
      {'value': 'SERVED', 'label': 'Served'},
    ];
  }

  /// Update queue status
  Future<void> _updateQueueStatus(String orderQueueId, String newStatus) async {
    try {
      final success = await OrderService.updateQueueStatus(orderQueueId, newStatus);

      if (success) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Queue status updated successfully'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Refresh the order data
        if (widget.onRefresh != null) {
          widget.onRefresh!();
        }
      } else {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update queue status'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error updating queue status: $e'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  /// Helper method to build individual item row
  Widget _buildItemRow(Map<String, dynamic> item, int? queueIndex) {
    final itemId = item['id'] as String;
    final billAssignment = billAssignments[itemId] ?? 0;

    final billColors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
    ];

    return Container(
      margin: EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: billAssignment > 0
            ? billColors[(billAssignment - 1) % billColors.length]
                .withOpacity(0.1)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Item name and basic info
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    item['name'],
                    style: GoogleFonts.dmSans(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                    ),
                  ),
                ),
                if (!isSplittingBill)
                  Text(
                    CurrencyFormatter.format(_getItemPrice(item)),
                    style: GoogleFonts.dmSans(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
              ],
            ),
            SizedBox(height: 4),

            // Quantity and base price
            Builder(
              builder: (context) {
                print('=== DEBUG at display point ===');
                print('Item at display: $item');
                print('Item keys at display: ${item.keys.toList()}');
                print('=== END DEBUG at display point ===');
                return Text(
                  'Quantity: ${item['quantity']} • Base Price: ${CurrencyFormatter.format(_getItemBase(item))} each',
                  style: GoogleFonts.dmSans(
                    color: Colors.grey[400],
                    fontSize: 14,
                  ),
                );
              },
            ),

            // Customization details
            if (item['customization'] != null) ...[
              SizedBox(height: 8),
              _buildCustomizationDetails(item['customization']),
            ],

            // Split bill controls
            if (isSplittingBill) ...[
              SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    CurrencyFormatter.format(_getItemPrice(item)),
                    style: GoogleFonts.dmSans(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Wrap(
                    spacing: 8,
                    children: List.generate(numberOfBills, (index) {
                      final billColors = [
                        Colors.blue,
                        Colors.green,
                        Colors.orange,
                        Colors.purple,
                        Colors.teal,
                      ];
                      final color =
                          billColors[index % billColors.length];

                      return _buildBillRadioButton(
                        itemId,
                        index + 1,
                        color,
                        'Bill ${index + 1}',
                      );
                    }),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCustomizationDetails(Map<String, dynamic> customization) {
    List<Widget> customizationWidgets = [];

    // Allergies
    if (customization['allergies'] != null &&
        (customization['allergies'] as List).isNotEmpty) {
      customizationWidgets.add(
        _buildCustomizationChip(
          'Allergies',
          (customization['allergies'] as List).map((a) => a['name']).join(', '),
          Colors.red,
          Icons.warning,
        ),
      );
    }

    // Spiciness
    if (customization['spiciness'] != null &&
        customization['spiciness'].toString().isNotEmpty) {
      customizationWidgets.add(
        _buildCustomizationChip(
          'Spiciness',
          customization['spiciness'].toString(),
          Colors.orange,
          Icons.local_fire_department,
        ),
      );
    }

    // Dish Addons
    if (customization['dishAddons'] != null &&
        (customization['dishAddons'] as List).isNotEmpty) {
      final addons = (customization['dishAddons'] as List)
          .map((a) => '${a['name']} (${a['quantity']}x)')
          .join(', ');
      customizationWidgets.add(
        _buildCustomizationChip(
          'Addons',
          addons,
          Colors.green,
          Icons.add_circle,
        ),
      );
    }

    // Dish Extras
    if (customization['dishExtras'] != null &&
        (customization['dishExtras'] as List).isNotEmpty) {
      final extras = (customization['dishExtras'] as List)
          .map((e) => '${e['name']} (${e['quantity']}x)')
          .join(', ');
      customizationWidgets.add(
        _buildCustomizationChip(
          'Extras',
          extras,
          Colors.blue,
          Icons.add_box,
        ),
      );
    }

    // Notes
    if (customization['notes'] != null &&
        customization['notes'].toString().isNotEmpty) {
      customizationWidgets.add(
        _buildCustomizationChip(
          'Notes',
          customization['notes'].toString(),
          Colors.purple,
          Icons.note,
        ),
      );
    }

    if (customizationWidgets.isEmpty) {
      return SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Customizations:',
          style: GoogleFonts.dmSans(
            color: Colors.grey[300],
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 4),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: customizationWidgets,
        ),
      ],
    );
  }

  Widget _buildCustomizationChip(
      String label, String value, Color color, IconData icon) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: color,
          ),
          SizedBox(width: 4),
          Text(
            '$label: ',
            style: GoogleFonts.dmSans(
              color: color,
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
          Flexible(
            child: Text(
              value,
              style: GoogleFonts.dmSans(
                color: Colors.grey[300],
                fontSize: 11,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBillRadioButton(
      String itemId, int billNumber, Color color, String tooltip) {
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: () {
          setState(() {
            if (billAssignments[itemId] == billNumber) {
              billAssignments[itemId] = 0;
            } else {
              billAssignments[itemId] = billNumber;
            }
            _updateBillTotals();
          });
        },
        child: Container(
          padding: EdgeInsets.all(4),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: billAssignments[itemId] == billNumber
                ? color
                : Colors.grey.withOpacity(0.2),
            border: Border.all(
              color: color.withOpacity(0.5),
              width: 2,
            ),
          ),
          child: billAssignments[itemId] == billNumber
              ? Icon(Icons.check, size: 16, color: Colors.white)
              : SizedBox(width: 16, height: 16),
        ),
      ),
    );
  }

  String _generateBillSummary() {
    List<String> billSummaries = [];
    for (int i = 0; i < numberOfBills; i++) {
      billSummaries
          .add('Bill ${i + 1}: ${CurrencyFormatter.format(billAmounts[i])}');
    }
    return billSummaries.join('\n');
  }

  Widget _buildPaymentSection() {
    // Use backend calculated total instead of frontend calculation
    final total = _getTotalAmount();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Row(
          children: [
            Expanded(
              child: _buildPaymentOption(
                'Cash',
                Icons.payments_outlined,
                Colors.green,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: _buildPaymentOption(
                'Card',
                Icons.credit_card_outlined,
                Colors.blue,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: _buildPaymentOption(
                'Split',
                Icons.call_split_outlined,
                Colors.orange,
              ),
            ),
          ],
        ),
        if (selectedPaymentMethod == 'Cash') ...[
          SizedBox(height: 16),
          _buildCashAmountField(total),
        ],
        if (selectedPaymentMethod == 'Split') ...[
          SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextField(
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'Cash Amount',
                    labelStyle: TextStyle(color: Colors.white70),
                    prefixIcon:
                        Icon(Icons.payments_outlined, color: Colors.green),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.white24),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.green),
                    ),
                  ),
                  style: TextStyle(color: Colors.white),
                  onChanged: (value) {
                    setState(() {
                      cashAmount = double.tryParse(value) ?? 0;
                      cardAmount = total - cashAmount;
                    });
                  },
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: TextField(
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'Card Amount',
                    labelStyle: TextStyle(color: Colors.white70),
                    prefixIcon:
                        Icon(Icons.credit_card_outlined, color: Colors.blue),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.white24),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.blue),
                    ),
                  ),
                  style: TextStyle(color: Colors.white),
                  onChanged: (value) {
                    setState(() {
                      cardAmount = double.tryParse(value) ?? 0;
                      cashAmount = total - cardAmount;
                    });
                  },
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Split Amount:',
                style: GoogleFonts.dmSans(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
              Text(
                '${CurrencyFormatter.format(cashAmount + cardAmount)} / ${CurrencyFormatter.format(total)}',
                style: GoogleFonts.dmSans(
                  color: (cashAmount + cardAmount) == total
                      ? Colors.green
                      : Colors.orange,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }


  Widget _buildAppliedDiscountsSection() {
    final appliedDiscounts = _getAppliedDiscounts();

    if (appliedDiscounts.isEmpty) {
      return SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Applied Discounts & Coupons",
          style: GoogleFonts.dmSans(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: appliedDiscounts.map((discount) {
            final code = discount['code'] ?? '';
            final name = discount['name'] ?? '';
            final type = discount['type'] ?? '';
            final value = discount['value'] ?? 0;
            final amount = discount['amount'] ?? 0;

            return Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.local_offer, size: 16, color: Colors.green),
                      SizedBox(width: 4),
                      Text(
                        code,
                        style: GoogleFonts.dmSans(
                          color: Colors.green,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  if (name.isNotEmpty) ...[
                    SizedBox(height: 2),
                    Text(
                      name,
                      style: GoogleFonts.dmSans(
                        color: Colors.white70,
                        fontSize: 10,
                      ),
                    ),
                  ],
                  SizedBox(height: 2),
                  Text(
                    type == 'PERCENTAGE'
                        ? '$value% off - ${CurrencyFormatter.format(double.tryParse(amount.toString()) ?? 0)}'
                        : 'Fixed ${CurrencyFormatter.format(double.tryParse(amount.toString()) ?? 0)} off',
                    style: GoogleFonts.dmSans(
                      color: Colors.green,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildBillSummary(double subtotal, double tax, double total, double totalDiscount, double backendTipAmount) {
    final taxBreakdown = _getTaxBreakdown();

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _buildBillRow('Subtotal', subtotal),
          SizedBox(height: 8),

          // Show discount if applied
          if (totalDiscount > 0) ...[
            _buildBillRow('Discount', -totalDiscount, textColor: Colors.green),
            SizedBox(height: 8),
          ],

          // Show tax breakdown from backend
          ...taxBreakdown.map((taxItem) {
            final rate = taxItem['rate'] ?? 0;
            final amount = taxItem['amount'] ?? 0;
            final taxCode = taxItem['taxCode'] ?? 'Tax';
            return Column(
              children: [
                _buildBillRow('$taxCode ($rate%)', double.tryParse(amount.toString()) ?? 0),
                SizedBox(height: 8),
              ],
            );
          }).toList(),

          // Show tip from backend if exists
          if (backendTipAmount > 0) ...[
            _buildBillRow('Tip', backendTipAmount),
            SizedBox(height: 8),
          ],

          Divider(color: Colors.grey[600], height: 24),
          _buildBillRow('Total', total, isTotal: true),
        ],
      ),
    );
  }

  Widget _buildActionButtons(double total) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        border: Border(top: BorderSide(color: Colors.grey[800]!)),
      ),
      child: Row(
        children: [
          if (!isSplittingBill) ...[
            Expanded(
              child: ElevatedButton.icon(
                icon: Icon(Icons.call_split, color: Colors.white),
                label: Text(
                  "Split Bill",
                  style: GoogleFonts.dmSans(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  padding: EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onPressed: () {
                  setState(() {
                    isSplittingBill = true;
                  });
                },
              ),
            ),
            SizedBox(width: 12),
          ],
          Expanded(
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[700],
                padding: EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onPressed: () => Navigator.pop(context),
              child: Text(
                "Cancel",
                style: GoogleFonts.dmSans(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              icon: Icon(Icons.monetization_on, color: Colors.white),
              label: Text(
                "Add Tip",
                style: GoogleFonts.dmSans(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green[600],
                padding: EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onPressed: () => _addTip(),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFF2CBF5A),
                padding: EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onPressed: isSplittingBill && billAssignments.isEmpty
                  ? null
                  : () => _processPayment(context, total),
              child: Text(
                isSplittingBill ? "Continue Split Payment" : "Process Payment",
                style: GoogleFonts.dmSans(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentOption(String title, IconData icon, Color color) {
    final isSelected = selectedPaymentMethod == title;
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedPaymentMethod = title;
          if (title == 'Split') {
            _showSplitAmountDialog();
          }
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 0.1.h, horizontal: 0.1.w),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.2) : Colors.grey[800],
          border: Border.all(
            color: isSelected ? color : Colors.transparent,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? color : Colors.grey[400],
              size: 28,
            ),
            SizedBox(height: 8),
            Text(
              title,
              style: GoogleFonts.dmSans(
                color: isSelected ? color : Colors.grey[400],
                fontWeight: FontWeight.bold,
              ),
            ),
            // Show split amounts if this is the split option and amounts are configured
            if (title == 'Split' &&
                isSelected &&
                cashAmount > 0 &&
                cardAmount > 0) ...[
              SizedBox(height: 4),
              Text(
                'Cash: ${CurrencyFormatter.format(cashAmount)}',
                style: GoogleFonts.dmSans(
                  color: Colors.green,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                'Card: ${CurrencyFormatter.format(cardAmount)}',
                style: GoogleFonts.dmSans(
                  color: Colors.blue,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTipButton(String label, double amount) {
    final isSelected = tipAmount == amount;
    return GestureDetector(
      onTap: () {
        setState(() {
          tipAmount = amount;
          tipController.text = amount.toStringAsFixed(2);
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? Color(0xFF2CBF5A).withOpacity(0.2)
              : Colors.grey[800],
          border: Border.all(
            color: isSelected ? Color(0xFF2CBF5A) : Colors.transparent,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          label,
          style: GoogleFonts.dmSans(
            color: isSelected ? Color(0xFF2CBF5A) : Colors.grey[400],
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildBillRow(String label, double amount, {bool isTotal = false, Color? textColor}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: GoogleFonts.dmSans(
            color: textColor ?? Colors.grey[400],
            fontSize: isTotal ? 18 : 16,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Text(
          CurrencyFormatter.format(amount),
          style: GoogleFonts.dmSans(
            color: textColor ?? (isTotal ? Colors.white : Colors.grey[400]),
            fontSize: isTotal ? 18 : 16,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ],
    );
  }

  Widget _buildMainTabSection(double subtotal, double tax, double total, double totalDiscount, double backendTipAmount) {
    return DefaultTabController(
      length: 2,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.grey[800],
              borderRadius: BorderRadius.circular(8),
            ),
            child: TabBar(
              indicatorSize: TabBarIndicatorSize.tab,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.white24.withOpacity(0.3),
              ),
              labelColor: Colors.white,
              unselectedLabelColor: Colors.grey[400],
              labelStyle: GoogleFonts.dmSans(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              unselectedLabelStyle: GoogleFonts.dmSans(
                fontWeight: FontWeight.normal,
                fontSize: 16,
              ),
              tabs: [
                Tab(
                  icon: Icon(Icons.receipt_long, size: 24),
                  text: 'Bill & Payment',
                ),
                Tab(
                  icon: Icon(Icons.local_offer, size: 24),
                  text: 'Promotions',
                ),
              ],
            ),
          ),
          SizedBox(height: 16),
          Container(
            height: 400,
            child: TabBarView(
              children: [
                _buildBillTab(subtotal, tax, total, totalDiscount, backendTipAmount),
                _buildPromotionsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBillTab(double subtotal, double tax, double total, double totalDiscount, double backendTipAmount) {
    // Use backend calculated total instead of frontend calculation
    final calculatedTotal = _getTotalAmount();

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Payment Section
          Text(
            "Payment Method",
            style: GoogleFonts.dmSans(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildPaymentOption(
                  'Cash',
                  Icons.payments_outlined,
                  Colors.green,
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildPaymentOption(
                  'Card',
                  Icons.credit_card_outlined,
                  Colors.blue,
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildPaymentOption(
                  'Split',
                  Icons.call_split_outlined,
                  Colors.orange,
                ),
              ),
            ],
          ),

          // Cash amount field for cash payment
          if (selectedPaymentMethod == 'Cash') ...[
            SizedBox(height: 16),
            _buildCashAmountField(calculatedTotal),
          ],

          // Split payment fields
          if (selectedPaymentMethod == 'Split') ...[
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Cash Amount',
                      labelStyle: TextStyle(color: Colors.white70),
                      prefixIcon: Icon(Icons.payments_outlined, color: Colors.green),
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.white24),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.green),
                      ),
                    ),
                    style: TextStyle(color: Colors.white),
                    onChanged: (value) {
                      setState(() {
                        cashAmount = double.tryParse(value) ?? 0;
                        cardAmount = calculatedTotal - cashAmount;
                      });
                    },
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: 'Card Amount',
                      labelStyle: TextStyle(color: Colors.white70),
                      prefixIcon: Icon(Icons.credit_card_outlined, color: Colors.blue),
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.white24),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.blue),
                      ),
                    ),
                    style: TextStyle(color: Colors.white),
                    onChanged: (value) {
                      setState(() {
                        cardAmount = double.tryParse(value) ?? 0;
                        cashAmount = calculatedTotal - cardAmount;
                      });
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total Split Amount:',
                  style: GoogleFonts.dmSans(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
                Text(
                  '${CurrencyFormatter.format(cashAmount + cardAmount)} / ${CurrencyFormatter.format(calculatedTotal)}',
                  style: GoogleFonts.dmSans(
                    color: (cashAmount + cardAmount) == calculatedTotal
                        ? Colors.green
                        : Colors.orange,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],

          SizedBox(height: 1.h),
          _buildBillSummary(subtotal, tax, total, totalDiscount, backendTipAmount),
          SizedBox(height: 1.h),
          _buildAppliedDiscountsSection(),
        ],

      ),
    );
  }

  Widget _buildPromotionsTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Coupon Section
          Text(
            "Apply Coupon",
            style: GoogleFonts.dmSans(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _couponController,
                  style: GoogleFonts.dmSans(color: Colors.white),
                  decoration: InputDecoration(
                    hintText: 'Enter coupon code',
                    hintStyle: GoogleFonts.dmSans(color: Colors.grey),
                    prefixIcon: Icon(Icons.local_offer, color: Colors.orange),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[700]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.orange),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12),
              ElevatedButton(
                onPressed: _promotionState.isApplyingCoupon ? null : _applyCoupon,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _promotionState.isApplyingCoupon
                    ? SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Apply',
                        style: GoogleFonts.dmSans(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ],
          ),
          if (_promotionState.appliedCoupons.isNotEmpty) ...[
            SizedBox(height: 12),
            Text(
              "Applied Coupons:",
              style: GoogleFonts.dmSans(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.orange,
              ),
            ),
            SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: _promotionState.appliedCoupons.map((coupon) => Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.orange.withOpacity(0.3)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.check_circle, size: 16, color: Colors.orange),
                    SizedBox(width: 4),
                    Text(
                      coupon,
                      style: GoogleFonts.dmSans(
                        color: Colors.orange,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              )).toList(),
            ),
          ],

          SizedBox(height: 1.h),

          // Tip Section
          Text(
            "Apply Tip Amount",
            style: GoogleFonts.dmSans(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _tipCodeController,
                  style: GoogleFonts.dmSans(color: Colors.white),
                  decoration: InputDecoration(
                    hintText: 'Enter tip Amount',
                    hintStyle: GoogleFonts.dmSans(color: Colors.grey),
                    prefixIcon: Icon(Icons.monetization_on, color: Colors.green),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[700]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.green),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12),
              ElevatedButton(
                onPressed: _promotionState.isApplyingTip ? null : _applyTip,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _promotionState.isApplyingTip
                    ? SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Apply',
                        style: GoogleFonts.dmSans(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ],
          ),
          if (_promotionState.appliedTips.isNotEmpty) ...[
            SizedBox(height: 12),
            Text(
              "Applied Tips:",
              style: GoogleFonts.dmSans(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.green,
              ),
            ),
            SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: _promotionState.appliedTips.map((tip) => Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.green.withOpacity(0.3)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.check_circle, size: 16, color: Colors.green),
                    SizedBox(width: 4),
                    Text(
                      tip,
                      style: GoogleFonts.dmSans(
                        color: Colors.green,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              )).toList(),
            ),
          ],

          SizedBox(height: 1.h),

          // Voucher Section
          Text(
            "Apply Voucher",
            style: GoogleFonts.dmSans(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _voucherController,
                  style: GoogleFonts.dmSans(color: Colors.white),
                  decoration: InputDecoration(
                    hintText: 'Enter voucher code',
                    hintStyle: GoogleFonts.dmSans(color: Colors.grey),
                    prefixIcon: Icon(Icons.card_giftcard, color: Colors.purple),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey[700]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.purple),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12),
              ElevatedButton(
                onPressed: _promotionState.isApplyingVoucher ? null : _applyVoucher,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _promotionState.isApplyingVoucher
                    ? SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Apply',
                        style: GoogleFonts.dmSans(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ],
          ),
          if (_promotionState.appliedVouchers.isNotEmpty) ...[
            SizedBox(height: 12),
            Text(
              "Applied Vouchers:",
              style: GoogleFonts.dmSans(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.purple,
              ),
            ),
            SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: _promotionState.appliedVouchers.map((voucher) => Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.purple.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.purple.withOpacity(0.3)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.check_circle, size: 16, color: Colors.purple),
                    SizedBox(width: 4),
                    Text(
                      voucher,
                      style: GoogleFonts.dmSans(
                        color: Colors.purple,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              )).toList(),
            ),
          ],
        ],
      ),
    );
  }



  Widget _buildCashAmountField(double total) {
    final balance = cashHandedAmount - total;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          controller: cashHandedController,
          keyboardType: TextInputType.number,
          style: GoogleFonts.dmSans(color: Colors.white),
          decoration: InputDecoration(
            labelText: 'Cash Amount Handed',
            labelStyle: TextStyle(color: Colors.white70),
            prefixIcon: Icon(Icons.payments_outlined, color: Colors.green),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.white24),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.green),
            ),
          ),
          onChanged: (value) {
            setState(() {
              cashHandedAmount = double.tryParse(value) ?? 0;
            });
          },
        ),
        if (cashHandedAmount > 0) ...[
          SizedBox(height: 12),
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: balance >= 0 ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: balance >= 0 ? Colors.green.withOpacity(0.3) : Colors.red.withOpacity(0.3),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total Amount:',
                  style: GoogleFonts.dmSans(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
                Text(
                  CurrencyFormatter.format(total),
                  style: GoogleFonts.dmSans(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 8),
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: balance >= 0 ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: balance >= 0 ? Colors.green.withOpacity(0.3) : Colors.red.withOpacity(0.3),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  balance >= 0 ? 'Change to Return:' : 'Amount Short:',
                  style: GoogleFonts.dmSans(
                    color: balance >= 0 ? Colors.green : Colors.red,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  CurrencyFormatter.format(balance.abs()),
                  style: GoogleFonts.dmSans(
                    color: balance >= 0 ? Colors.green : Colors.red,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  // Widget _buildPaymentSection() {
  //   double calculateTotal() {
  //     final subtotal = (widget.order['items'] as List).fold(0.0, (sum, item) {
  //       final price = (item['price'] ?? 0.0) as double;
  //       final quantity = (item['quantity'] ?? 1) as int;
  //       return sum + (price * quantity);
  //     });
  //
  //     final tax = subtotal * 0.1; // 10% tax
  //     final total =
  //         subtotal + tax + tipAmount; // tipAmount should be a class variable
  //
  //     return total;
  //   }
  //
  //   final total = calculateTotal();
  //
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.stretch,
  //     children: [
  //       Row(
  //         children: [
  //           Expanded(
  //             child: _buildPaymentOption(
  //               'Cash',
  //               Icons.payments_outlined,
  //               Colors.green,
  //             ),
  //           ),
  //           SizedBox(width: 12),
  //           Expanded(
  //             child: _buildPaymentOption(
  //               'Card',
  //               Icons.credit_card_outlined,
  //               Colors.blue,
  //             ),
  //           ),
  //           SizedBox(width: 12),
  //           Expanded(
  //             child: _buildPaymentOption(
  //               'Split',
  //               Icons.call_split_outlined,
  //               Colors.orange,
  //             ),
  //           ),
  //         ],
  //       ),
  //       if (selectedPaymentMethod == 'Split') ...[
  //         SizedBox(height: 16),
  //         Row(
  //           children: [
  //             Expanded(
  //               child: TextField(
  //                 keyboardType: TextInputType.number,
  //                 decoration: InputDecoration(
  //                   labelText: 'Cash Amount',
  //                   labelStyle: TextStyle(color: Colors.white70),
  //                   prefixIcon:
  //                       Icon(Icons.payments_outlined, color: Colors.green),
  //                   enabledBorder: OutlineInputBorder(
  //                     borderSide: BorderSide(color: Colors.white24),
  //                   ),
  //                   focusedBorder: OutlineInputBorder(
  //                     borderSide: BorderSide(color: Colors.green),
  //                   ),
  //                 ),
  //                 style: TextStyle(color: Colors.white),
  //                 onChanged: (value) {
  //                   setState(() {
  //                     cashAmount = double.tryParse(value) ?? 0;
  //                     cardAmount = total - cashAmount;
  //                   });
  //                 },
  //               ),
  //             ),
  //             SizedBox(width: 12),
  //             Expanded(
  //               child: TextField(
  //                 keyboardType: TextInputType.number,
  //                 decoration: InputDecoration(
  //                   labelText: 'Card Amount',
  //                   labelStyle: TextStyle(color: Colors.white70),
  //                   prefixIcon:
  //                       Icon(Icons.credit_card_outlined, color: Colors.blue),
  //                   enabledBorder: OutlineInputBorder(
  //                     borderSide: BorderSide(color: Colors.white24),
  //                   ),
  //                   focusedBorder: OutlineInputBorder(
  //                     borderSide: BorderSide(color: Colors.blue),
  //                   ),
  //                 ),
  //                 style: TextStyle(color: Colors.white),
  //                 onChanged: (value) {
  //                   setState(() {
  //                     cardAmount = double.tryParse(value) ?? 0;
  //                     cashAmount = total - cardAmount;
  //                   });
  //                 },
  //               ),
  //             ),
  //           ],
  //         ),
  //         SizedBox(height: 8),
  //         Row(
  //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //           children: [
  //             Text(
  //               'Total Split Amount:',
  //               style: GoogleFonts.dmSans(
  //                 color: Colors.white70,
  //                 fontSize: 14,
  //               ),
  //             ),
  //             Text(
  //               '\$${(cashAmount + cardAmount).toStringAsFixed(2)} / \$${total.toStringAsFixed(2)}',
  //               style: GoogleFonts.dmSans(
  //                 color: (cashAmount + cardAmount) == total
  //                     ? Colors.green
  //                     : Colors.orange,
  //                 fontSize: 14,
  //                 fontWeight: FontWeight.bold,
  //               ),
  //             ),
  //           ],
  //         ),
  //       ],
  //     ],
  //   );
  // }

  Future<void> _processPayment(BuildContext context, double total) async {
    if (selectedPaymentMethod == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Please select a payment method')),
      );
      return;
    }

    // Get order detail ID
    final orderDetailId = widget.order['orderDetailId'] as String?;
    if (orderDetailId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Order detail ID not found'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      isProcessingPayment = true;
    });

    try {
      // Convert selected payment method to PaymentMethod enum
      PaymentMethod? paymentMethod;
      switch (selectedPaymentMethod?.toUpperCase()) {
        case 'CASH':
          paymentMethod = PaymentMethod.cash;
          break;
        case 'CARD':
          paymentMethod = PaymentMethod.card;
          break;
        case 'UPI':
          paymentMethod = PaymentMethod.upi;
          break;
        case 'WALLET':
          paymentMethod = PaymentMethod.wallet;
          break;
        case 'BANK TRANSFER':
          paymentMethod = PaymentMethod.bankTransfer;
          break;
        case 'SPLIT':
          paymentMethod = PaymentMethod.split;
          break;
        default:
          paymentMethod = PaymentMethod.cash; // Default fallback
      }

      debugPrint('💳 Processing payment for order $orderDetailId with method ${paymentMethod.value}');

      // Call the payment completion API
      final success = await PaymentService.completePayment(
        orderDetailId: orderDetailId,
        paymentMethod: paymentMethod,
      );

      if (success) {
        // Payment successful - show success dialog
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: Colors.grey[900],
            title: Row(
              children: [
                Icon(Icons.check_circle, color: Color(0xFF2CBF5A)),
                SizedBox(width: 8),
                Text(
                  'Payment Successful',
                  style: GoogleFonts.dmSans(color: Colors.white),
                ),
              ],
            ),
            content: Text(
              isSplittingBill
                  ? 'Payment processed successfully!\n${_generateBillSummary()}'
                  : selectedPaymentMethod == 'Split'
                      ? 'Payment processed successfully!\nCash: ${CurrencyFormatter.format(cashAmount)}\nCard: ${CurrencyFormatter.format(cardAmount)}'
                      : 'Payment of ${CurrencyFormatter.format(total)} processed successfully via ${PaymentService.getPaymentMethodDisplayName(paymentMethod)}!',
              style: GoogleFonts.dmSans(color: Colors.white70),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  GoRouter.of(context).pop();
                  // Refresh the orders list to reflect the payment status
                  final runningOrdersPage = context.findAncestorStateOfType<_RunningOrdersPageState>();
                  runningOrdersPage?._runningOrdersBloc.add(RefreshOrders());
                },
                child: Text(
                  'Done',
                  style: GoogleFonts.dmSans(
                    color: Color(0xFF2CBF5A),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        );
      } else {
        // Payment failed - show error
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment failed. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ Error processing payment: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error processing payment: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        isProcessingPayment = false;
      });
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'ready':
        return Color(0xFF2CBF5A);
      case 'cooking':
        return Colors.orange;
      case 'in progress':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  void _showSplitAmountDialog() {
    final TextEditingController cashController = TextEditingController();
    final TextEditingController cardController = TextEditingController();

    // Initialize with current values if they exist
    if (cashAmount > 0) {
      cashController.text = cashAmount.toStringAsFixed(2);
    }
    if (cardAmount > 0) {
      cardController.text = cardAmount.toStringAsFixed(2);
    }

    // Use backend calculated total instead of frontend calculation
    final double total = _getTotalAmount();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          'Split Payment',
          style: GoogleFonts.dmSans(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Total Amount: ${CurrencyFormatter.format(total)}',
              style: GoogleFonts.dmSans(color: Colors.white70),
            ),
            SizedBox(height: 16),
            TextField(
              controller: cashController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'Cash Amount',
                labelStyle: TextStyle(color: Colors.white70),
                prefixIcon: Icon(Icons.payments_outlined, color: Colors.green),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white24),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.green),
                ),
              ),
              style: TextStyle(color: Colors.white),
              onChanged: (value) {
                double cash = double.tryParse(value) ?? 0;
                cardController.text = (total - cash).toStringAsFixed(2);
              },
            ),
            SizedBox(height: 12),
            TextField(
              controller: cardController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'Card Amount',
                labelStyle: TextStyle(color: Colors.white70),
                prefixIcon:
                    Icon(Icons.credit_card_outlined, color: Colors.blue),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white24),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.blue),
                ),
              ),
              style: TextStyle(color: Colors.white),
              onChanged: (value) {
                double card = double.tryParse(value) ?? 0;
                cashController.text = (total - card).toStringAsFixed(2);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.dmSans(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
            ),
            onPressed: () {
              final cash = double.tryParse(cashController.text) ?? 0;
              final card = double.tryParse(cardController.text) ?? 0;
              final totalSplit = cash + card;

              // Validate that the split amounts equal the total
              if ((totalSplit - total).abs() > 0.01) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        'Split amounts must equal total amount (${CurrencyFormatter.format(total)})'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              setState(() {
                cashAmount = cash;
                cardAmount = card;
              });

              // Show confirmation
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      'Split payment configured: Cash \$${cash.toStringAsFixed(2)}, Card \$${card.toStringAsFixed(2)}'),
                  backgroundColor: Colors.green,
                ),
              );

              Navigator.pop(context);
            },
            child: Text(
              'Confirm',
              style: GoogleFonts.dmSans(),
            ),
          ),
        ],
      ),
    );
  }
}

// Filter Options Modal
class FilterOptionsModal extends StatefulWidget {
  final Function(Map<String, dynamic>) onApply;

  const FilterOptionsModal({super.key, required this.onApply});

  @override
  State<FilterOptionsModal> createState() => _FilterOptionsModalState();
}

class _FilterOptionsModalState extends State<FilterOptionsModal> {
  List<String> selectedStatuses = [];
  String? sortBy;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Filter Orders",
            style: GoogleFonts.dmSans(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16),
          Text(
            "Status",
            style: GoogleFonts.dmSans(
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          Wrap(
            spacing: 8,
            children: [
              _buildFilterChip("Ready", "Ready"),
              _buildFilterChip("In Progress", "In Progress"),
              _buildFilterChip("Cooking", "Cooking"),
              _buildFilterChip("Cancelled", "Cancelled"),
              _buildFilterChip("Completed", "Completed"),
            ],
          ),
          SizedBox(height: 16),
          Text(
            "Sort by",
            style: GoogleFonts.dmSans(
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          Wrap(
            spacing: 8,
            children: [
              _buildSortChip("Newest First", "newest"),
              _buildSortChip("Oldest First", "oldest"),
              _buildSortChip("Table Number", "table"),
            ],
          ),
          SizedBox(height: 1.h),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 12),
                  ),
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text("Cancel"),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    padding: EdgeInsets.symmetric(vertical: 12),
                  ),
                  onPressed: () {
                    widget.onApply({
                      'statuses': selectedStatuses,
                      'sortBy': sortBy,
                    });
                    Navigator.pop(context);
                  },
                  child: Text("Apply"),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final isSelected = selectedStatuses.contains(value);
    return FilterChip(
      label: Text(
        label,
        style: GoogleFonts.dmSans(
          color: isSelected ? Colors.white : Colors.black,
        ),
      ),
      selected: isSelected,
      backgroundColor: Colors.grey[200],
      selectedColor: Colors.blue,
      onSelected: (selected) {
        setState(() {
          if (selected) {
            selectedStatuses.add(value);
          } else {
            selectedStatuses.remove(value);
          }
        });
      },
    );
  }

  Widget _buildSortChip(String label, String value) {
    final isSelected = sortBy == value;
    return FilterChip(
      label: Text(
        label,
        style: GoogleFonts.dmSans(
          color: isSelected ? Colors.white : Colors.black,
        ),
      ),
      selected: isSelected,
      backgroundColor: Colors.grey[200],
      selectedColor: Colors.green,
      onSelected: (selected) {
        setState(() {
          sortBy = selected ? value : null;
        });
      },
    );
  }
}

// Add a new PaymentModal widget for handling individual bill payments
class PaymentModal extends StatefulWidget {
  final double total;
  final String billLabel;

  const PaymentModal({
    required this.total,
    required this.billLabel,
    Key? key,
  }) : super(key: key);

  @override
  _PaymentModalState createState() => _PaymentModalState();
}

class _PaymentModalState extends State<PaymentModal> {
  String? selectedPaymentMethod;

  @override
  Widget build(BuildContext context) {
    throw UnimplementedError();
  }

  // Implement the payment modal UI here...
}
